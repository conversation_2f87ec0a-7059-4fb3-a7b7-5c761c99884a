import { storeToRefs } from "pinia";
import { computed, onMounted, shallowRef } from "vue";

import type {
  OperationResponse,
  OperationAddRequest,
  OperationDeleteRequest,
  OperationGetRequest,
  OperationListRequest,
  OperationUpdateRequest,
} from "@/api/bcare-types-v2";
import { useOperationStore } from "@/stores/operation-store-v2";

interface UseOperationOptions {
  autoLoad?: boolean;
}

export default function useOperation(options: UseOperationOptions = { autoLoad: true }) {
  const operationStore = useOperationStore();
  const { isLoading, error, operations } = storeToRefs(operationStore);

  // Local state for filtered operations, initialized as empty
  const filteredOperations = shallowRef<OperationResponse[]>([]);

  // Computed property for operation count
  const operationCount = computed(() => operationStore.getOperationCount);

  // Load all operations
  const loadOperations = async () => {
    if (operationStore.hasCachedData) {
      operationStore.initializeFromCache();
    } else {
      await operationStore.fetchAllOperations();
    }
    // Keep filteredOperations empty after loading
    filteredOperations.value = [];
  };

  // Auto-load operations if autoLoad is true
  if (options.autoLoad) {
    onMounted(() => {
      loadOperations();
    });
  }

  // Add a new operation
  const addOperation = async (req: OperationAddRequest) => {
    return await operationStore.addOperation(req);
  };

  // Delete an operation
  const deleteOperation = async (req: OperationDeleteRequest) => {
    await operationStore.deleteOperation(req);
  };

  // Get a specific operation
  const getOperation = async (req: OperationGetRequest) => {
    return await operationStore.getOperation(req);
  };

  // List operations with filtering
  const listOperations = async (req: OperationListRequest) => {
    const response = await operationStore.listOperations(req);
    if (response?.operations) {
      filteredOperations.value = response.operations;
    }
    return response;
  };

  // Update an operation
  const updateOperation = async (req: OperationUpdateRequest) => {
    return await operationStore.updateOperation(req);
  };

  // Get an operation by ID
  const getOperationById = (id: number) => {
    return operationStore.getOperationById(id);
  };

  // Get multiple operations by their IDs
  const getOperationsByIds = (ids: number[]) => {
    return ids.map((id) => operationStore.getOperationById(id)).filter(Boolean) as OperationResponse[];
  };

  // Search operations
  const searchOperations = (query: string) => {
    const lowercaseQuery = query.toLowerCase();
    filteredOperations.value = operationStore.operations.filter(
      (operation) =>
        operation.name.toLowerCase().includes(lowercaseQuery) ||
        operation.group.some(group => group.toLowerCase().includes(lowercaseQuery)),
    );
  };

  // Sort operations
  const sortOperations = (key: keyof OperationResponse, order: "asc" | "desc" = "asc") => {
    filteredOperations.value = [...filteredOperations.value].sort((a, b) => {
      if (a[key] < b[key]) return order === "asc" ? -1 : 1;
      if (a[key] > b[key]) return order === "asc" ? 1 : -1;
      return 0;
    });
  };

  // Paginate operations
  const paginateOperations = (page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    return filteredOperations.value.slice(startIndex, startIndex + pageSize);
  };

  const getOperationNameById = (id: number): string => {
    const operation = operationStore.getOperationById(id);
    return operation ? operation.name : "Unknown";
  };

  return {
    isLoading,
    error,
    operations, // Exposed operations list from the store
    filteredOperations,
    operationCount,
    loadOperations,
    addOperation,
    deleteOperation,
    getOperation,
    listOperations,
    updateOperation,
    getOperationById,
    getOperationsByIds,
    searchOperations,
    sortOperations,
    paginateOperations,
    getOperationNameById,
  };
}
