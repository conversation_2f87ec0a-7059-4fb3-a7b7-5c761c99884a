// constants/material.ts
export const MATERIAL_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
} as const;

export type MaterialStatus = (typeof MATERIAL_STATUS)[keyof typeof MATERIAL_STATUS];

export const MATERIAL_STATUS_OPTIONS = [
  { name: "Hoạt động", value: MATERIAL_STATUS.ACTIVE },
  { name: "Không hoạt động", value: MATERIAL_STATUS.INACTIVE },
] as const;

export const COMMON_UNITS = [
  "kg",
  "g",
  "lít",
  "ml",
  "hộp",
  "chai",
  "túi",
  "gói",
  "thùng",
  "cái",
  "chiếc",
  "bộ",
  "m",
  "cm",
  "m²",
  "m³",
] as const;

export interface MaterialFilters {
  status?: MaterialStatus;
  unit?: string;
  name?: string;
  code?: string;
}
