import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  Operation,
  OperationAddRequest,
  OperationDeleteRequest,
  OperationGetRequest,
  OperationListRequest,
  OperationUpdateRequest} from "@/api/bcare-types-v2";
import { operationAdd, operationDelete, operationGet, operationList, operationUpdate } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface CacheData {
  operations: Operation[];
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 giờ

export const useOperationStore = defineStore("operation", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const operations = shallowRef<Operation[]>([]);
  const currentOperation = shallowRef<Operation | null>(null);
  const filteredOperations = shallowRef<Operation[]>([]);

  const cachedData = useStorage<CacheData>("operationStoreCache", { operations: [], expireTime: 0 });

  // Getters
  const getOperationCount = computed(() => operations.value.length);
  const getOperationById = computed(() => (id: number) => operations.value.find((operation) => operation.id === id));
  const hasCachedData = computed(() => {
    return cachedData.value.expireTime > Date.now() && cachedData.value.operations.length > 0;
  });

  // Cache functions
  function initializeFromCache() {
    if (hasCachedData.value) {
      operations.value = cachedData.value.operations;
    }
  }

  function updateCache() {
    cachedData.value = {
      operations: operations.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  // Actions
  function fetchAllOperations() {
    return performAsyncAction(async () => {
      const response = await operationList({ page: 1, page_size: 1000 });
      if (response.data?.operations) {
        operations.value = response.data.operations;
        updateCache();
      }
      return response.data;
    });
  }

  function addOperation(req: OperationAddRequest) {
    return performAsyncAction(async () => {
      const response = await operationAdd(req);
      if (response.data) {
        operations.value.push(response.data);
        updateCache();
      }
      return response.data;
    });
  }

  function deleteOperation(req: OperationDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await operationDelete(req);
      if (response.data) {
        operations.value = operations.value.filter((operation) => operation.id !== req.id);
        updateCache();
      }
      return response.data;
    });
  }

  function getOperation(req: OperationGetRequest) {
    return performAsyncAction(async () => {
      const response = await operationGet(req);
      currentOperation.value = response.data ?? null;
      return response.data;
    });
  }

  function listOperations(req: OperationListRequest) {
    return performAsyncAction(async () => {
      const response = await operationList(req);
      if (response.data?.operations) {
        filteredOperations.value = response.data.operations;
      }
      return response.data;
    });
  }

  function updateOperation(req: OperationUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await operationUpdate(req);
      if (response.data) {
        const index = operations.value.findIndex((operation) => operation.id === req.id);
        if (index !== -1) {
          operations.value[index] = response.data;
          updateCache();
        }
      }
      return response.data;
    });
  }

  function updateFullOperationList() {
    return performAsyncAction(async () => {
      const response = await operationList({ page: 1, page_size: 1000 });
      if (response.data?.operations) {
        operations.value = response.data.operations;
        updateCache();
      }
      return response.data;
    });
  }

  return {
    // State
    operations,
    currentOperation,
    filteredOperations,
    isLoading,
    error,
    // Getters
    getOperationCount,
    getOperationById,
    hasCachedData,
    // Actions
    initializeFromCache,
    fetchAllOperations,
    addOperation,
    deleteOperation,
    getOperation,
    listOperations,
    updateOperation,
    updateFullOperationList,
  };
});
