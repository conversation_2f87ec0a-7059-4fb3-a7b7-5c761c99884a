import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  OperationAddRequest,
  OperationDeleteRequest,
  OperationGetRequest,
  OperationListRequest,
  OperationResponse,
  OperationUpdateRequest} from "@/api/bcare-types-v2";
import { operationAdd, operationDelete, operationGet, operationList, operationUpdate } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface CacheData {
  operations: OperationResponse[];
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 giờ

export const useOperationStore = defineStore("operation", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const operations = shallowRef<OperationResponse[]>([]);
  const currentOperation = shallowRef<OperationResponse | null>(null);
  const filteredOperations = shallowRef<OperationResponse[]>([]);

  const cachedData = useStorage<CacheData>("operationStoreCache", { operations: [], expireTime: 0 });

  // Getters
  const getOperationCount = computed(() => Array.isArray(operations.value) ? operations.value.length : 0);
  const getOperationById = computed(() => (id: number) =>
    Array.isArray(operations.value) ? operations.value.find((operation) => operation.id === id) : undefined
  );
  const hasCachedData = computed(() => {
    return cachedData.value.expireTime > Date.now() &&
           Array.isArray(cachedData.value.operations) &&
           cachedData.value.operations.length > 0;
  });

  // Cache functions
  function initializeFromCache() {
    if (hasCachedData.value && Array.isArray(cachedData.value.operations)) {
      operations.value = cachedData.value.operations;
    }
  }

  function updateCache() {
    cachedData.value = {
      operations: operations.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  // Actions
  function fetchAllOperations() {
    return performAsyncAction(async () => {
      const response = await operationList({ page: 1, page_size: 1000 });
      if (response.data?.operations && Array.isArray(response.data.operations)) {
        operations.value = response.data.operations;
        updateCache();
      } else {
        operations.value = [];
      }
      return response.data;
    });
  }

  function addOperation(req: OperationAddRequest) {
    return performAsyncAction(async () => {
      const response = await operationAdd(req);
      if (response.data) {
        if (!Array.isArray(operations.value)) {
          operations.value = [];
        }
        operations.value.push(response.data);
        updateCache();
      }
      return response.data;
    });
  }

  function deleteOperation(req: OperationDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await operationDelete(req);
      // operationDelete returns null, so we just filter the local array
      if (Array.isArray(operations.value)) {
        operations.value = operations.value.filter((operation) => operation.id !== req.id);
        updateCache();
      }
      return response;
    });
  }

  function getOperation(req: OperationGetRequest) {
    return performAsyncAction(async () => {
      const response = await operationGet(req);
      currentOperation.value = response.data ?? null;
      return response.data;
    });
  }

  function listOperations(req: OperationListRequest) {
    return performAsyncAction(async () => {
      const response = await operationList(req);
      if (response.data?.operations && Array.isArray(response.data.operations)) {
        filteredOperations.value = response.data.operations;
      } else {
        filteredOperations.value = [];
      }
      return response.data;
    });
  }

  function updateOperation(req: OperationUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await operationUpdate(req);
      if (response.data && Array.isArray(operations.value)) {
        const index = operations.value.findIndex((operation) => operation.id === req.id);
        if (index !== -1) {
          operations.value[index] = response.data;
          updateCache();
        }
      }
      return response.data;
    });
  }

  function updateFullOperationList() {
    return performAsyncAction(async () => {
      const response = await operationList({ page: 1, page_size: 1000 });
      if (response.data?.operations && Array.isArray(response.data.operations)) {
        operations.value = response.data.operations;
        updateCache();
      } else {
        operations.value = [];
      }
      return response.data;
    });
  }

  return {
    // State
    operations,
    currentOperation,
    filteredOperations,
    isLoading,
    error,
    // Getters
    getOperationCount,
    getOperationById,
    hasCachedData,
    // Actions
    initializeFromCache,
    fetchAllOperations,
    addOperation,
    deleteOperation,
    getOperation,
    listOperations,
    updateOperation,
    updateFullOperationList,
  };
});
