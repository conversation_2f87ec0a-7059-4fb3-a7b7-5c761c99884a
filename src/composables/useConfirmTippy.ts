import tippy, { Instance } from "tippy.js";
import { onBeforeUnmount, ref } from "vue";

import "tippy.js/dist/tippy.css";

interface ConfirmOptions {
  title?: string;
  icon?: string;
  acceptLabel?: string;
  rejectLabel?: string;
  onAccept?: () => void;
  onReject?: () => void;
  skipPreventDefault?: boolean;
}

export function useConfirmTippy() {
  const tippyInstance = ref<Instance | null>(null);
  let isDestroying = false;

  const destroyInstance = () => {
    if (tippyInstance.value && !isDestroying) {
      isDestroying = true;
      tippyInstance.value.destroy();
      tippyInstance.value = null;
      isDestroying = false;
    }
  };

  const createConfirmContent = (options: ConfirmOptions) => {
    const wrapper = document.createElement("div");
    wrapper.className = "p-1";

    wrapper.innerHTML = `
      <div class="flex flex-col gap-3 p-2">
        <div class="flex items-center gap-2">
          <i class="${options.icon || "pi pi-exclamation-triangle"} text-warning text-xl"></i>
          <span class="font-medium">${options.title || "Xác nhận"}</span>
        </div>
        <div class="flex justify-end gap-2">
          <button class="reject-btn px-3 py-1 rounded border border-gray-300 hover:bg-gray-100 text-sm">
            ${options.rejectLabel || "Hủy"}
          </button>
          <button class="accept-btn px-3 py-1 rounded bg-primary text-white hover:bg-primary-600 text-sm">
            ${options.acceptLabel || "Xác nhận"}
          </button>
        </div>
      </div>
    `;

    return wrapper;
  };

  const confirm = (event: MouseEvent | undefined, options: ConfirmOptions) => {
    // Handle event if exists
    if (event && !options.skipPreventDefault) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Get target element
    const target = (event?.currentTarget as Element) || document.body;

    // Destroy existing instance
    destroyInstance();

    const content = createConfirmContent(options);

    // Create new instance
    tippyInstance.value = tippy(target, {
      content,
      trigger: "manual",
      interactive: true,
      arrow: true,
      placement: "auto",
      appendTo: () => document.body,
      theme: "light",
      allowHTML: true,
      animation: "scale",
      maxWidth: "none",
      hideOnClick: false,
      popperOptions: {
        strategy: "fixed",
        modifiers: [
          {
            name: "flip",
            enabled: true,
            options: {
              fallbackPlacements: ["top", "bottom", "left", "right"],
            },
          },
          {
            name: "preventOverflow",
            enabled: true,
            options: {
              boundary: "viewport",
              altAxis: true,
            },
          },
        ],
      },
      onMount(instance) {
        const acceptBtn = content.querySelector(".accept-btn");
        const rejectBtn = content.querySelector(".reject-btn");

        const hideAndDestroy = () => {
          instance.hide();
          setTimeout(() => {
            if (tippyInstance.value === instance) {
              destroyInstance();
            }
          }, 300);
        };

        acceptBtn?.addEventListener("click", () => {
          options.onAccept?.();
          hideAndDestroy();
        });

        rejectBtn?.addEventListener("click", () => {
          options.onReject?.();
          hideAndDestroy();
        });

        const handleClickOutside = (e: MouseEvent) => {
          if (!content.contains(e.target as Node)) {
            hideAndDestroy();
          }
        };
        document.addEventListener("click", handleClickOutside);

        instance.setProps({
          onDestroy: () => {
            document.removeEventListener("click", handleClickOutside);
          },
        });
      },
    });

    tippyInstance.value.show();
  };

  onBeforeUnmount(() => {
    destroyInstance();
  });

  return {
    confirm,
  };
}
