<script setup lang="ts">
import { nextTick, onMounted, ref, defineAsyncComponent } from "vue";

import { TaskResponse } from "@/api/bcare-types-v2";
import { useTaskStore } from "@/stores/task-store";

import ActivityHeader from "./ActivityHeader.vue";
import TaskItem from "./TaskItem.vue";
import Empty from "@/base-components/Empty";

const taskStore = useTaskStore();
const props = defineProps<{
  personId?: number;
}>();

const TaskCreateForm = defineAsyncComponent(
  () => import("@/pages/task/components/TaskCreateForm.vue"),
);
const TaskUpdateForm = defineAsyncComponent(
  () => import("@/pages/task/components/TaskUpdateForm.vue"),
);

// Add ref for TaskCreateForm
const taskCreateForm = ref<InstanceType<typeof TaskCreateForm>>();
const selectedTask = ref<TaskResponse | undefined>();

// Add handler function
const handleShowTask = () => {
  taskCreateForm.value?.open();
};

const handleDoneTask = async (id: number) => {
  await taskStore.updateTask({ id, state: "completed", modified: ["state"] });
  handleReloadData();
};

const handleDeleteTask = async (id: number) => {
  await taskStore.deleteTasks({ id_list: [id] });
  handleReloadData();
};

// Add handler for edit
const handleEditTask = (task: TaskResponse) => {
  selectedTask.value = task;
};

const handleCloseForm = () => {
  selectedTask.value = undefined;
};

// Add handler for reloading data
const handleReloadData = async () => {
  await taskStore.fetchQueryTasks(
    {
      table: "task_serial_view",
      filters: [
        {
          field: "task_serial_view.person_id",
          operator: "EQ",
          value: props.personId?.toString(),
        },
      ],
      sort: [
        {
          field: "task_serial_view.start_date",
          order: "DESC",
        },
        {
          field: "task_serial_view.serial",
          order: "DESC",
        },
      ],
      limit: 30,
      offset: 0,
    },
    true,
  );
};

onMounted(async () => {
  await handleReloadData();
});
</script>

<template>
  <div class="mt-4">
    <ActivityHeader title="" button-label="Thêm công việc" @button-click="handleShowTask" />
    <div v-if="taskStore.tasks.length > 0">
      <TaskItem
        v-for="task in taskStore.tasks"
        :key="task.id"
        :task="task"
        @update-status="handleDoneTask(task.id)"
        @delete="handleDeleteTask(task.id)"
        @edit="handleEditTask(task)"
      />
    </div>
    <div v-else><Empty /></div>
  </div>

  <TaskCreateForm
    ref="taskCreateForm"
    @reload-data="handleReloadData"
    :person-id="props.personId"
  />

  <TaskUpdateForm
    v-if="selectedTask"
    :task-data-prop="selectedTask"
    :key="selectedTask.id"
    @reload-data="handleReloadData"
    @close="handleCloseForm"
  />
</template>
