<!-- <template>
  <div class="p-5">
    <h2 class="mb-4 text-xl font-medium">Tag Management</h2>
    <TagsDisplay :tags="tags" size="normal" shape="pill" :editable="true" :groupByCategory="true" />
  </div>

  <div
    class="my-5 flex h-full flex-1 overflow-hidden overflow-y-auto overflow-x-clip rounded-2xl border bg-surface-0 border-surface dark:bg-surface-950"
  >
    <div class="flex w-4/12 min-w-40 flex-col gap-6 overflow-auto xl:w-3/12">
      <div
        class="sticky top-0 z-10 -mb-2 flex flex-col gap-6 bg-surface-0 px-5 pb-2 pt-3 dark:bg-surface-950"
      >
        <div class="flex items-center justify-between gap-6 text-color">
          <div class="lead text-2xl font-medium">Chats</div>
          <Button icon="pi pi-plus" text />
        </div>
      </div>
      <div class="px-5">
        <IconField iconPosition="left">
          <InputIcon class="pi pi-search"> </InputIcon>
          <InputText v-model="search" placeholder="Search" class="w-full" />
        </IconField>
      </div>
      <div class="w-full px-5">
        <SelectButton
          v-model="value"
          :options="options"
          aria-labelledby="basic"
          :pt="{
            root: {
              class: 'w-full',
            },
            pcbutton: {
              root: {
                class: 'flex-1',
              },
            },
          }"
        />
      </div>
      <div class="flex flex-1 flex-col">
        <div
          v-for="chat in chats"
          :key="chat.name"
          class="flex cursor-pointer items-center gap-2 p-4 transition-all hover:bg-emphasis"
          :class="{
            'bg-emphasis': chat.name === activeChat,
          }"
        >
          <div class="relative">
            <div
              v-if="chat.active !== undefined"
              class="absolute right-0 top-0 flex items-center justify-center rounded-full bg-surface-0 p-[1px] dark:bg-surface-950"
            >
              <Badge :severity="chat.active ? 'success' : 'danger'" class="p-1.5"></Badge>
            </div>
            <Avatar
              v-bind="chat.image ? { image: chat.image } : { label: chat.capName }"
              :class="{
                '!bg-primary-100 !text-primary-950': !chat.image,
              }"
              class="flex text-base font-medium"
              size="large"
              shape="circle"
            />
          </div>
          <div class="flex-1">
            <div class="flex items-start justify-between gap-1">
              <div class="font-medium leading-6 text-color">{{ chat.name }}</div>
              <div class="text-sm leading-5 text-muted-color">{{ chat.time }}</div>
            </div>
            <div class="mt-1 flex items-center justify-between gap-5">
              <div class="line-clamp-1 text-sm leading-5 text-muted-color">
                {{ chat.lastMessage }}
              </div>
              <Badge
                v-if="chat.unreadMessageCount > 0"
                :value="chat.unreadMessageCount"
                severity="contrast"
              ></Badge>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex w-8/12 flex-col border-x border-surface xl:w-6/12">
      <div class="flex items-center gap-7 border-b p-4 border-surface">
        <div class="flex items-center">
          <Avatar
            image="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png"
            class="av mr-2"
            size="large"
            shape="circle"
          />
          <div class="flex-1">
            <div
              class="cursor-pointer leading-6 transition-colors text-color hover:text-muted-color-emphasis"
            >
              PrimeTek
            </div>
            <div class="mt-1 line-clamp-1 leading-5 text-muted-color">
              Cody Fisher, Esther Howard, Jerome Bell, Kristin Watson, Ronald Richards, Darrell
              Steward
            </div>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <Button icon="pi pi-phone" text />
          <Button icon="pi pi-search" text />
          <Button
            type="button"
            icon="pi pi-ellipsis-h"
            text
            @click="toggle"
            aria-haspopup="true"
            aria-controls="overlay_menu"
          />
          <Menu ref="menu" id="overlay_menu" :model="menuItems" :popup="true" />
        </div>
      </div>
      <div class="flex flex-1 flex-col gap-8 overflow-y-auto px-6 py-8">
        <div
          v-for="message in chatMessages"
          :key="message.id"
          class="flex w-fit min-w-64 max-w-[60%] items-start"
          :class="{ 'ml-auto mr-0 flex-row-reverse': message.type === 'sent' }"
        >
          <div
            class="sticky top-0 flex items-center gap-2 transition-all"
            :class="{
              'flex-row-reverse': message.type === 'sent',
            }"
          >
            <Avatar
              v-bind="message.image ? { image: message.image } : { label: message.capName }"
              :class="{
                'bg-primary-100 text-primary-950': !message.image,
              }"
              class="h-10 w-10 text-sm font-medium"
              shape="circle"
            />
            <div>
              <svg
                :class="
                  message.type === 'received'
                    ? 'fill-surface-100 dark:fill-surface-800'
                    : 'rotate-180 fill-primary'
                "
                class=""
                xmlns="http://www.w3.org/2000/svg"
                width="7"
                height="11"
                viewBox="0 0 7 11"
                fill="none"
              >
                <path
                  d="M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z"
                />
              </svg>
            </div>
          </div>
          <div
            :class="
              message.type === 'received'
                ? 'flex-1 rounded-lg bg-surface-100 px-2 py-1 dark:bg-surface-800'
                : 'flex-1 rounded-lg bg-primary px-2 py-1'
            "
          >
            <p
              :class="
                message.type === 'received'
                  ? 'mb-0 leading-6 text-color'
                  : 'mb-0 leading-6 text-primary-contrast'
              "
            >
              {{ message.message }}
            </p>
            <div
              v-if="message.attachment"
              :class="
                message.type === 'received'
                  ? 'bg-surface-200 dark:bg-surface-700'
                  : 'bg-primary-emphasis'
              "
              class="mb-0.5 mt-2 w-full rounded-lg transition-all hover:opacity-75"
            >
              <img
                class="block h-auto w-full cursor-pointer"
                :src="message.attachment"
                alt="Message Image"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="flex items-end justify-between gap-2 border-t p-4 border-surface">
        <div class="flex flex-1 items-end gap-1">
          <Button icon="pi pi-face-smile" text />
          <Button icon="pi pi-paperclip" text />
          <Textarea
            class="ml-1 max-h-32 min-h-9 flex-1 overflow-auto border-0 shadow-none bg-emphasis"
            autoResize
            rows="1"
            placeholder="Write your message..."
          />
        </div>
        <Button icon="pi pi-send" />
      </div>
    </div>
    <div class="hidden w-3/12 min-w-40 overflow-auto px-3 py-6 xl:block">
      <div class="flex flex-col items-center justify-center">
        <Avatar
          image="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png"
          class="h-32 w-32"
          size="xlarge"
          shape="circle"
        />
        <div class="mt-4 w-full text-center font-medium leading-6 text-color">PrimeTek</div>
        <div class="mt-1 w-full text-center text-sm leading-5 text-muted-color">@primetek</div>
        <div class="mt-4 flex flex-wrap items-center justify-center gap-1">
          <Button icon="pi pi-phone text-muted-color" severity="secondary" text />
          <Button icon="pi pi-video text-muted-color" severity="secondary" text />
          <Button icon="pi pi-sign-in text-muted-color" severity="secondary" text />
          <Button icon="pi pi-info-circle text-muted-color" severity="secondary" text />
          <Button
            type="button"
            icon="pi pi-ellipsis-v text-muted-color"
            severity="secondary"
            text
            @click="toggle"
            aria-haspopup="true"
            aria-controls="overlay_menu"
          />
          <Menu ref="menu" id="overlay_menu" :model="menuItems" :popup="true" />
        </div>
      </div>
      <div class="mt-4 flex flex-col gap-4">
        <div class="flex items-center gap-2">
          <i class="pi pi-bell text-color"></i>
          <div class="flex-1 font-medium leading-6 text-color">Notification</div>
          <ToggleSwitch v-model="notification" />
        </div>
        <div class="flex items-center gap-2">
          <i class="pi pi-volume-down text-color"></i>
          <div class="flex-1 font-medium leading-6 text-color">Sound</div>
          <ToggleSwitch v-model="sound" />
        </div>
        <div class="flex items-center gap-2">
          <i class="pi pi-download text-color"></i>
          <div class="flex-1 font-medium leading-6 text-color">Save to downloads</div>
          <ToggleSwitch v-model="download" />
        </div>
      </div>
      <div class="mt-6">
        <div class="flex items-center gap-2">
          <div class="flex-1 font-medium leading-6 text-color">Members</div>
          <Button label="See All" class="px-2 py-0.5 text-sm text-muted-color" text />
        </div>
        <div class="mt-4 flex flex-col gap-4">
          <div
            v-for="member in members"
            :key="member.name"
            class="flex cursor-pointer items-center gap-2"
          >
            <Avatar
              v-bind="member.image ? { image: member.image } : { label: member.capName }"
              :class="{
                'bg-orange-100 text-orange-950': !member.image,
              }"
              class="text-xs font-medium"
              shape="circle"
            />
            <div
              class="flex-1 text-sm font-medium leading-5 transition-colors text-color hover:text-muted-color-emphasis"
            >
              {{ member.name }}
            </div>
            <i class="pi pi-chevron-right text-xs text-muted-color"></i>
          </div>
        </div>
      </div>
      <div class="mt-5">
        <SelectButton
          v-model="media"
          :options="mediaOptions"
          :pt="{
            root: {
              class: 'w-full',
            },
            pcbutton: {
              root: {
                class: 'flex-1',
              },
            },
          }"
        />
        <div class="mb-5 mt-3 grid grid-cols-3 gap-2">
          <div
            v-for="(media, index) in chatMedia"
            :key="index"
            class="aspect-square flex-1 cursor-pointer rounded-lg border transition-all border-surface bg-emphasis hover:opacity-70"
          >
            <img class="block h-full w-full object-cover" :src="media" alt="Media Image" />
          </div>
          <div
            class="flex aspect-square flex-1 cursor-pointer items-center justify-center rounded-lg border transition-all border-surface bg-emphasis hover:opacity-70"
          >
            <span class="font-medium text-muted-color">99+</span>
          </div>
        </div>
        <Button
          label="Show more"
          icon="pi pi-arrow-right"
          iconPos="right"
          outlined
          class="w-full text-left"
          :pt="{
            root: {
              class: 'justify-between',
            },
          }"
        />
      </div>
    </div>
  </div>
</template>

<script>
import TagsDisplay from "@/components/Tags/TagsDisplay.vue";
import { useTagStore } from "@/stores/tag-store";
import { storeToRefs } from "pinia";
import { onMounted } from "vue";

export default {
  name: "Chat",
  redrawListener: null,
  components: {
    TagsDisplay,
  },
  setup() {
    const tagStore = useTagStore();
    const { tags } = storeToRefs(tagStore);

    onMounted(() => {
      if (tagStore.hasCachedData) {
        tagStore.initializeFromCache();
      } else {
        tagStore.fetchAllTags();
      }
    });

    return { tags };
  },
  data() {
    return {
      search: "",
      download: false,
      notification: true,
      sound: false,
      value: "Chat",
      value2: "",
      options: ["Chat", "Call"],
      media: "Media",
      mediaOptions: ["Media", "Link", "Docs"],
      activeChat: "PrimeTek Team",
      menuItems: [
        {
          label: "Group Info",
          icon: "pi pi-info-circle",
        },
        {
          label: "Leave group",
          icon: "pi pi-sign-out",
        },
      ],
      chats: [
        {
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg",
          name: "Cody Fisher",
          capName: "CF",
          active: true,
          unreadMessageCount: 8,
          time: "12.30",
          lastMessage: "Hey there! I've heard about PrimeVue. Any cool tips for getting started?",
        },
        {
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png",
          name: "PrimeTek Team",
          capName: "PT",
          active: undefined,
          unreadMessageCount: 0,
          time: "11.15",
          lastMessage: "Let's implement PrimeVue. Elevating our UI game! 🚀",
        },
        {
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar2.png",
          name: "Jerome Bell",
          capName: "JB",
          active: true,
          unreadMessageCount: 4,
          time: "11.15",
          lastMessage: "Absolutely! PrimeVue's documentation is gold—simplifies our UI work.",
        },
        {
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar12.jpg",
          name: "Robert Fox",
          capName: "RF",
          active: false,
          unreadMessageCount: 0,
          time: "11.15",
          lastMessage: "Interesting! PrimeVue sounds amazing. What's your favorite feature?",
        },
        {
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar13.jpg",
          name: "Esther Howard",
          capName: "EH",
          active: true,
          unreadMessageCount: 9,
          time: "11.15",
          lastMessage: "Quick one, team! Anyone using PrimeVue for mobile app development?",
        },
        {
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar9.jpg",
          name: "Darlene Robertson",
          capName: "DR",
          active: false,
          unreadMessageCount: 0,
          time: "11.15",
          lastMessage:
            "Just explored PrimeVue's themes. Can we talk about those stunning designs? 😍",
        },
        {
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar6.png",
          name: "Ralph Edwards",
          capName: "RE",
          active: false,
          unreadMessageCount: 0,
          time: "11.15",
          lastMessage: "PrimeVue is a game-changer, right? What are your thoughts, folks?",
        },
        {
          image: "",
          name: "Ronald Richards",
          capName: "RR",
          active: false,
          unreadMessageCount: 0,
          time: "11.15",
          lastMessage:
            "Jumping in! PrimeVue's community forum is buzzing. Any engaging discussions?",
        },
        {
          image: "",
          name: "Kristin Watson",
          capName: "KW",
          active: false,
          unreadMessageCount: 0,
          time: "11.15",
          lastMessage: "Sharing a quick win-PrimeVue tutorials are leveling up my UI skills. 👩‍💻",
        },
        {
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar7.png",
          name: "Darrell Steward",
          capName: "DS",
          active: false,
          unreadMessageCount: 0,
          time: "11.15",
          lastMessage: "Reflecting on PrimeVue's impact on our workflow. What's your take?",
        },
      ],
      chatMessages: [
        {
          id: 1,
          attachment: "",
          name: "",
          image: "",
          capName: "OS",
          type: "received",
          message: "Awesome! What's the standout feature?",
        },
        {
          id: 2,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar8.png",
          capName: "A",
          type: "received",
          message: "PrimeVue rocks! Simplifies UI dev with versatile components.",
        },
        {
          id: 3,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg",
          capName: "A",
          type: "received",
          message: "Intriguing! Tell us more about its impact.",
        },
        {
          id: 4,
          attachment:
            "https://www.primefaces.org/cdn/primevue/images/landing/apps/message-image.png",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar2.png",
          capName: "A",
          type: "received",
          message:
            "It's design-neutral and compatible with Tailwind. Features accessible, high-grade components!",
        },
        {
          id: 5,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png",
          capName: "A",
          type: "sent",
          message: "Customizable themes, responsive design – UI excellence!",
        },
        {
          id: 6,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar8.png",
          capName: "A",
          type: "received",
          message: "Love it! Fast-tracking our development is key.",
        },
        {
          id: 7,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar6.png",
          capName: "A",
          type: "received",
          message: "Documentation rocks too – smooth integration for all.",
        },
        {
          id: 8,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png",
          capName: "B",
          type: "sent",
          message:
            "The flexibility and ease of use are truly impressive. Have you explored the new components?",
        },
        {
          id: 9,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar12.jpg",
          capName: "C",
          type: "received",
          message: "Absolutely, the new calendar component has saved us a ton of development time!",
        },
        {
          id: 10,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar13.jpg",
          capName: "D",
          type: "received",
          message:
            "And the accessibility features are top-notch. It's great to see a library focusing on inclusivity.",
        },
        {
          id: 11,
          attachment:
            "https://www.primefaces.org/cdn/primevue/images/landing/apps/message-image.png",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png",
          capName: "E",
          type: "sent",
          message:
            "I couldn't agree more. Plus, the documentation is incredibly thorough, which makes onboarding new team members a breeze.",
        },
        {
          id: 12,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar6.png",
          capName: "F",
          type: "received",
          message:
            "Do you have any tips for optimizing performance when using multiple complex components?",
        },
        {
          id: 13,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg",
          capName: "G",
          type: "received",
          message:
            "Yes! Lazy loading and code splitting can make a huge difference, especially in larger applications.",
        },
        {
          id: 14,
          attachment: "",
          name: "",
          image: "",
          capName: "HS",
          type: "received",
          message:
            "I've also found that leveraging the component's internal state management capabilities can help streamline data flow and improve performance.",
        },
        {
          id: 15,
          attachment: "",
          name: "",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png",
          capName: "H",
          type: "sent",
          message:
            "That's great advice. It's amazing how much detail and thought has gone into making PrimeVue such a powerful tool for developers.",
        },
      ],
      chatMedia: [
        "https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image1.png",
        "https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image2.png",
        "https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image3.png",
        "https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image4.png",
        "https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image5.png",
      ],
      members: [
        {
          name: "Robin Jonas",
          capName: "RJ",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar2.png",
        },
        {
          name: "Cameron Williamson",
          capName: "CW",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg",
        },
        {
          name: "Eleanor Pena",
          capName: "EP",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png",
        },
        {
          name: "Arlene McCoy",
          capName: "AM",
          image: "https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar8.png",
        },
        { name: "Dianne Russell", capName: "DR", image: "" },
      ],
    };
  },
  methods: {
    toggle(event) {
      this.$refs.menu.toggle(event);
    },
  },
};
</script>

 -->

<template>
  <div class="customer-visit-history bg-surface-ground rounded-lg p-3 md:p-4 lg:p-5">
    <!-- Smaller Title and Margin -->
    <h3
      class="mb-4 text-lg font-semibold text-surface-800 dark:text-surface-100 md:mb-6 md:text-xl"
    >
      Lịch sử Đến Phòng Khám
    </h3>

    <!-- Loading State - Smaller -->
    <div
      v-if="isLoading"
      class="flex min-h-[150px] flex-col items-center justify-center text-surface-500 dark:text-surface-400"
    >
      <!-- Smaller Spinner -->
      <ProgressSpinner style="width: 40px; height: 40px" strokeWidth="6" animationDuration=".5s" />
      <!-- Smaller Text -->
      <span class="mt-1 text-sm">Đang tải dữ liệu...</span>
    </div>

    <!-- Error State - Smaller -->
    <div
      v-else-if="errorLoading"
      class="flex min-h-[150px] flex-col items-center justify-center text-red-600 dark:text-red-400"
    >
      <!-- Smaller Icon -->
      <i class="pi pi-exclamation-triangle text-2xl"></i>
      <!-- Smaller Text -->
      <span class="mt-1 text-center text-sm"
        >Đã xảy ra lỗi khi tải lịch sử khám. <br class="hidden sm:inline" />Vui lòng thử lại.</span
      >
    </div>

    <!-- Timeline - Using updated PT for compactness -->
    <Timeline
      v-else-if="sortedHistory.length > 0"
      :value="sortedHistory"
      align="alternate"
      layout="vertical"
      :pt="timelinePT"
    >
      <!-- Smaller Marker -->
      <template #marker="slotProps">
        <span
          :class="[
            'z-10 flex h-8 w-8 items-center justify-center rounded-full shadow-sm', // Reduced size, shadow-sm
            `bg-${slotProps.item.color || 'primary-500'}`,
            'text-white',
          ]"
        >
          <!-- Smaller Icon -->
          <i :class="[slotProps.item.icon || 'pi pi-check', 'text-base']"></i>
        </span>
      </template>

      <!-- Smaller Card Content -->
      <template #content="slotProps">
        <!-- Using updated PT for compactness -->
        <Card :pt="cardPT">
          <template #title>
            <div class="flex flex-col justify-between gap-1 sm:flex-row sm:items-center">
              <!-- Smaller Font for Date -->
              <span
                class="order-2 text-sm font-medium text-primary-600 dark:text-primary-400 sm:order-1 sm:text-base sm:font-semibold"
              >
                {{ formatDate(slotProps.item.date) }}
              </span>
              <!-- Smaller Tag -->
              <Tag
                :value="slotProps.item.status"
                severity="info"
                class="order-1 !px-1.5 !py-0.5 !text-xs sm:order-2"
              />
              <!-- Alternative smaller span tag:
                 <span class="order-1 sm:order-2 text-[11px] font-medium bg-blue-100 text-blue-700 dark:bg-blue-700 dark:text-blue-100 py-0.5 px-1.5 rounded-full self-start sm:self-center">
                    {{ slotProps.item.status }}
                 </span> -->
            </div>
          </template>
          <template #subtitle>
            <!-- Smaller Font and Margin for Doctor -->
            <span
              v-if="slotProps.item.details.doctor"
              class="mb-2 mt-1 block text-xs italic text-surface-500 dark:text-surface-400"
            >
              Bác sĩ: {{ slotProps.item.details.doctor }}
            </span>
          </template>
          <template #content>
            <!-- Reduced spacing -->
            <div class="space-y-3">
              <!-- Dịch vụ - Smaller -->
              <div v-if="slotProps.item.details.services?.length > 0">
                <h5
                  class="mb-1.5 flex items-center text-xs font-medium text-surface-700 dark:text-surface-200"
                >
                  <i class="pi pi-list mr-1.5 text-sm text-primary-500"></i>Dịch vụ đã sử dụng:
                </h5>
                <!-- Reduced list spacing and font -->
                <ul class="list-none space-y-0.5 pl-4">
                  <li
                    v-for="(service, index) in slotProps.item.details.services"
                    :key="`service-${slotProps.item.id}-${index}`"
                    class="relative text-xs text-surface-600 before:absolute before:left-[-0.8em] before:font-bold before:text-primary-500 before:content-['•'] dark:text-surface-300"
                  >
                    {{ service }}
                  </li>
                </ul>
              </div>

              <!-- Divider - Using updated PT -->
              <Divider
                v-if="shouldShowDivider(slotProps.item.details, 'services', 'treatments')"
                :pt="dividerPT"
              />

              <!-- Điều trị - Smaller -->
              <div v-if="slotProps.item.details.treatments?.length > 0">
                <h5
                  class="mb-1.5 flex items-center text-xs font-medium text-surface-700 dark:text-surface-200"
                >
                  <i class="pi pi-shield mr-1.5 text-sm text-primary-500"></i>Điều trị/Kết quả:
                </h5>
                <!-- Reduced list spacing and font -->
                <ul class="list-none space-y-0.5 pl-4">
                  <li
                    v-for="(treatment, index) in slotProps.item.details.treatments"
                    :key="`treatment-${slotProps.item.id}-${index}`"
                    class="relative text-xs text-surface-600 before:absolute before:left-[-0.8em] before:font-bold before:text-primary-500 before:content-['•'] dark:text-surface-300"
                  >
                    {{ treatment }}
                  </li>
                </ul>
              </div>

              <!-- Divider - Using updated PT -->
              <Divider
                v-if="shouldShowDivider(slotProps.item.details, 'treatments', 'notes')"
                :pt="dividerPT"
              />

              <!-- Ghi chú - Smaller -->
              <div v-if="slotProps.item.details.notes">
                <h5
                  class="mb-1.5 flex items-center text-xs font-medium text-surface-700 dark:text-surface-200"
                >
                  <i class="pi pi-file-edit mr-1.5 text-sm text-primary-500"></i>Ghi chú của bác sĩ:
                </h5>
                <!-- Smaller font, tighter line-height -->
                <p
                  class="whitespace-pre-wrap text-xs leading-snug text-surface-600 dark:text-surface-300"
                >
                  {{ slotProps.item.details.notes }}
                </p>
              </div>
            </div>
          </template>
        </Card>
      </template>
    </Timeline>

    <!-- Empty State - Smaller -->
    <div
      v-else
      class="flex min-h-[150px] flex-col items-center justify-center text-center text-surface-500 dark:text-surface-400"
    >
      <!-- Smaller Icon -->
      <i class="pi pi-folder-open mb-2 text-3xl"></i>
      <!-- Smaller Text -->
      <p class="text-sm">Chưa có lịch sử khám nào được ghi nhận.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import Timeline from "primevue/timeline";
import Card from "primevue/card";
import Tag from "primevue/tag";
import Divider from "primevue/divider";
import ProgressSpinner from "primevue/progressspinner";
import type { VisitHistoryItem, VisitDetail } from "./interfaces/visit"; // Adjust path if needed

// --- Reactive State (unchanged) ---
const visitHistory = ref<VisitHistoryItem[]>([]);
const isLoading = ref<boolean>(true);
const errorLoading = ref<boolean>(false);

// --- Pass Through Definitions (Updated for compactness) ---

// PT for Timeline component
const timelinePT = {
  root: { class: "relative" },
  event: (options: any) => ({
    class: [
      "flex relative min-h-[50px]", // Reduced min-height
      // Reduced spacing between events
      { "mb-4 md:mb-5": options.context.index !== sortedHistory.value.length - 1 },
    ],
  }),
  opposite: {
    // Reduced padding
    class:
      "flex-1 md:w-1/2 md:pr-4 text-surface-600 dark:text-surface-300 pt-0.5 order-2 md:order-1 text-xs md:text-sm", // Adjusted font size hint
  },
  separator: {
    // Narrower separator column
    class: "flex flex-col items-center w-10 order-1 md:order-2",
  },
  // Marker handled in template slot
  connector: {
    class: "grow bg-surface-300 dark:bg-surface-700 w-px",
  },
  content: {
    // Reduced padding
    class: "flex-1 md:w-1/2 md:pl-4 pb-3 order-1 md:order-2",
  },
};

// PT for Card component inside Timeline
const cardPT = {
  root: {
    class: [
      "bg-surface-0 dark:bg-surface-800",
      "rounded-md shadow-sm border border-surface-200 dark:border-surface-700", // Smaller radius, shadow
      "overflow-hidden",
      "transition-shadow duration-200 ease-in-out hover:shadow", // Less prominent hover
    ],
  },
  // Reduced padding
  header: { class: "p-3 md:p-4" },
  title: { class: "" },
  subtitle: { class: "mb-0" }, // Keep as is
  // Reduced padding, no top padding
  content: { class: "p-3 md:p-4 pt-0" },
  body: { class: "" },
};

// PT for Divider component
const dividerPT = {
  root: {
    class: [
      "relative my-3", // Reduced margin
      "before:absolute before:left-0 before:top-1/2 before:w-full",
      "before:h-px",
      "before:bg-surface-200 dark:before:bg-surface-700",
      "before:-translate-y-1/2",
    ],
  },
  content: { class: "px-1.5 bg-surface-0 dark:bg-surface-800 relative z-[1] text-xs" }, // Smaller padding/font if content used
};

// --- Mock Data Fetching (unchanged) ---
const fetchVisitHistory = async (): Promise<VisitHistoryItem[]> => {
  console.log("Fetching visit history START...");
  await new Promise((resolve) => setTimeout(resolve, 1200));

  try {
    const mockData: VisitHistoryItem[] = [
      {
        id: 4,
        date: new Date(2024, 7, 28), // August 28, 2024
        status: "Kiểm tra sau điều trị",
        icon: "pi pi-check-circle",
        color: "teal-500", // Different color example
        details: {
          services: ["Khám tổng quát", "Chụp X-quang nhỏ"],
          treatments: ["Theo dõi thêm"],
          notes: "Tiến triển tốt, không cần can thiệp thêm.",
          doctor: "BS. Minh Trang",
        },
      },
      {
        id: 3,
        date: new Date(2024, 6, 15), // July 15, 2024
        status: "Tái khám định kỳ",
        icon: "pi pi-sync",
        color: "green-500",
        details: {
          services: ["Khám lâm sàng", "Đo huyết áp"],
          treatments: ["Điều chỉnh liều lượng thuốc X"],
          notes: "Huyết áp ổn định hơn. Tiếp tục theo dõi.",
          doctor: "BS. Lan Anh",
        },
      },
      {
        id: 2,
        date: new Date(2024, 4, 20), // May 20, 2024
        status: "Điều trị sâu răng",
        icon: "pi pi-cog",
        color: "amber-500",
        details: {
          services: ["Khám nha khoa", "Gây tê tại chỗ"],
          treatments: ["Trám răng hàm số 6", "Kê đơn giảm đau nhẹ"],
          notes: "Răng sâu đã được xử lý. Hẹn tái khám nếu đau nhức.",
          doctor: "BS. Tuấn Minh",
        },
      },
      {
        id: 1,
        date: new Date(2024, 0, 10), // January 10, 2024
        status: "Khám tổng quát đầu năm",
        icon: "pi pi-heart-fill",
        color: "blue-500",
        details: {
          services: ["Xét nghiệm máu cơ bản", "Siêu âm ổ bụng"],
          treatments: [], // No treatments needed
          notes: "Kết quả xét nghiệm và siêu âm trong giới hạn bình thường. Sức khỏe tốt.",
          doctor: "BS. Hoàng Hà",
        },
      },
      {
        id: 0,
        date: new Date(2023, 10, 5), // November 5, 2023
        status: "Tư vấn dinh dưỡng",
        icon: "pi pi-users",
        color: "purple-500",
        details: {
          services: ["Tư vấn chế độ ăn"],
          treatments: [],
          notes:
            "Đã tư vấn về chế độ ăn giảm tinh bột, tăng cường rau xanh và protein. Cung cấp thực đơn mẫu.",
          doctor: "BS. Ngọc Mai",
        },
      },
    ];
    console.log("Mock data GENERATED:", mockData);
    // if (Math.random() > 0.8) throw new Error("Simulated fetch error");
    return mockData;
  } catch (error) {
    console.error("Error GENERATING mock data:", error);
    throw error;
  }
};

// --- Lifecycle Hook (unchanged) ---
onMounted(async () => {
  console.log("onMounted: Starting data fetch...");
  isLoading.value = true;
  errorLoading.value = false;
  try {
    const data = await fetchVisitHistory();
    console.log("onMounted: Data received:", data);
    visitHistory.value = data;
    console.log("onMounted: visitHistory updated:", visitHistory.value);
  } catch (err) {
    console.error("onMounted: Failed to fetch history:", err);
    errorLoading.value = true;
    visitHistory.value = [];
  } finally {
    isLoading.value = false;
    console.log(
      "onMounted: Fetch finished. isLoading:",
      isLoading.value,
      "errorLoading:",
      errorLoading.value,
    );
  }
});

// --- Computed Properties (unchanged) ---
const sortedHistory = computed(() => {
  const history = Array.isArray(visitHistory.value) ? visitHistory.value : [];
  return [...history].sort((a, b) => b.date.getTime() - a.date.getTime());
});

// --- Helper Functions (unchanged) ---
const formatDate = (date: Date): string => {
  if (!date) return "";
  return date.toLocaleDateString("vi-VN", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
};

const shouldShowDivider = (
  details: VisitDetail,
  sectionAbove: keyof VisitDetail,
  sectionBelow: keyof VisitDetail,
): boolean => {
  const hasAbove = Array.isArray(details[sectionAbove])
    ? (details[sectionAbove] as string[]).length > 0
    : !!details[sectionAbove];
  const hasBelow = Array.isArray(details[sectionBelow])
    ? (details[sectionBelow] as string[]).length > 0
    : !!details[sectionBelow];
  return hasAbove && hasBelow;
};
</script>
