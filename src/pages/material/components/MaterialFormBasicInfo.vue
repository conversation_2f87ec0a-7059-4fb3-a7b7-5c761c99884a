<template>
  <div class="space-y-4">
    <FormField label="Tên nguyên liệu" icon="pi pi-box">
      <InputText
        v-model="formData.name"
        placeholder="Nhập tên nguyên liệu"
        class="w-full"
        :class="{ 'p-invalid': errors.name }"
      />
      <small class="p-error">{{ errors.name }}</small>
    </FormField>

    <div class="flex flex-col gap-4 sm:flex-row">
      <FormField label="Mã nguyên liệu" icon="pi pi-hashtag" class="flex-1">
        <InputText
          v-model="formData.code"
          placeholder="Nhập mã nguyên liệu"
          class="w-full"
          :class="{ 'p-invalid': errors.code }"
        />
        <small class="p-error">{{ errors.code }}</small>
      </FormField>

      <FormField label="Đơn vị tính" icon="pi pi-tag" class="flex-1">
        <InputText
          v-model="formData.unit"
          placeholder="Nhập đơn vị tính (kg, lít, hộp...)"
          class="w-full"
          :class="{ 'p-invalid': errors.unit }"
        />
        <small class="p-error">{{ errors.unit }}</small>
      </FormField>
    </div>

    <FormField label="Quy cách đóng gói" icon="pi pi-package">
      <InputText
        v-model="formData.packaging_specification"
        placeholder="Nhập quy cách đóng gói"
        class="w-full"
      />
    </FormField>

    <FormField label="Mô tả" icon="pi pi-align-left">
      <Textarea
        v-model="formData.description"
        rows="3"
        placeholder="Nhập mô tả nguyên liệu"
        class="w-full"
      />
    </FormField>

    <FormField label="Trạng thái" icon="pi pi-check-circle">
      <SelectButton
        v-model="formData.status"
        :options="statusOptions"
        optionLabel="label"
        optionValue="value"
        dataKey="value"
      >
        <template #option="{ option }">
          <div class="flex items-center gap-2">
            <i :class="[option.icon, option.value === 1 ? 'text-green-500' : 'text-gray-500']"></i>
            <span :class="[option.value === 1 ? 'text-green-600' : 'text-gray-600', 'font-medium']">
              {{ option.label }}
            </span>
          </div>
        </template>
      </SelectButton>
    </FormField>
  </div>
</template>

<script setup lang="ts">
import InputText from "primevue/inputtext";
import SelectButton from "primevue/selectbutton";
import Textarea from "primevue/textarea";

import type { MaterialAddRequest } from "@/api/bcare-types-v2";
import FormField from "@/components/Form/FormField.vue";

interface Props {
  formData: MaterialAddRequest;
  errors: Partial<Record<string, string>>;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:formData", value: MaterialAddRequest): void;
}>();

const statusOptions = [
  {
    label: "Hoạt động",
    value: 1,
    icon: "pi pi-check-circle",
  },
  {
    label: "Không hoạt động",
    value: 0,
    icon: "pi pi-ban",
  },
];
</script>
