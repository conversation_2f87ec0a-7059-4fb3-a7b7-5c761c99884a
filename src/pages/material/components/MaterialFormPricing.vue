<template>
  <div class="space-y-4">
    <FormField label="<PERSON><PERSON><PERSON> thành" icon="pi pi-dollar">
      <InputNumber
        v-model="formData.cost_price"
        :min="0"
        mode="currency"
        currency="VND"
        locale="vi-VN"
        class="w-full"
        :class="{ 'p-invalid': errors.cost_price }"
        placeholder="Nhập giá thành"
      />
      <small class="p-error">{{ errors.cost_price }}</small>
    </FormField>

    <div class="rounded-lg bg-blue-50 p-4">
      <div class="flex items-start gap-3">
        <i class="pi pi-info-circle mt-0.5 text-blue-500"></i>
        <div class="text-sm text-blue-700">
          <p class="mb-1 font-medium">Thông tin về giá thành:</p>
          <ul class="list-inside list-disc space-y-1 text-xs">
            <li><PERSON><PERSON><PERSON> thành là giá gốc của nguyên liệu</li>
            <li><PERSON><PERSON><PERSON><PERSON> sử dụng để tính toán chi phí sản xuất</li>
            <li><PERSON><PERSON> thể để trống nếu chưa xác định được giá</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import InputNumber from "primevue/inputnumber";

import type { MaterialAddRequest } from "@/api/bcare-types-v2";
import FormField from "@/components/Form/FormField.vue";

interface Props {
  formData: MaterialAddRequest;
  errors: Partial<Record<string, string>>;
}

defineProps<Props>();
</script>
