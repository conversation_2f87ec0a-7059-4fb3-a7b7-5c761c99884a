# Material Management Pages

Th<PERSON> mục này chứa các trang và components để quản lý nguyên liệu (materials) trong hệ thống.

## Cấu trúc thư mục

```
src/pages/material/
├── Materials.vue                    # Trang danh sách nguyên liệu
├── components/
│   ├── MaterialFormDrawer.vue       # Form drawer chính để thêm/sửa
│   ├── MaterialFormBasicInfo.vue    # Form thông tin cơ bản
│   ├── MaterialFormPricing.vue      # Form thông tin giá cả
│   └── index.ts                     # Export components
└── README.md                        # Tài liệu này
```

## Tính năng

### Materials.vue
- ✅ Hiển thị danh sách nguyên liệu với DataTable
- ✅ Tìm kiếm và lọc theo tên, đơn vị, trạng thái
- ✅ Phân trang
- ✅ Thê<PERSON>, sử<PERSON>, xóa nguyên liệu
- ✅ Hiển thị thông tin: tê<PERSON>, mã, <PERSON><PERSON><PERSON>, đ<PERSON><PERSON> vị, quy cách đóng gói, trạng thái

### MaterialFormDrawer.vue
- ✅ Form drawer để thêm/cập nhật nguyên liệu
- ✅ Validation form
- ✅ Tự động load dữ liệu khi edit
- ✅ Reset form khi đóng

### MaterialFormBasicInfo.vue
- ✅ Form thông tin cơ bản: tên, mã, đơn vị tính, quy cách đóng gói, mô tả, trạng thái

### MaterialFormPricing.vue
- ✅ Form thông tin giá thành
- ✅ Hiển thị gợi ý về giá thành

## Cách sử dụng

### 1. Import và sử dụng trang Materials
```vue
<template>
  <Materials />
</template>

<script setup lang="ts">
import Materials from '@/pages/material/Materials.vue'
</script>
```

### 2. Sử dụng riêng lẻ các components
```vue
<template>
  <MaterialFormDrawer
    v-model:visible="showForm"
    :material-id="selectedId"
    @success="handleSuccess"
  />
</template>

<script setup lang="ts">
import { MaterialFormDrawer } from '@/pages/material/components'
</script>
```

## Dependencies

### Hooks & Stores
- `useMaterial` - Hook để quản lý materials
- `useMaterialStore` - Pinia store cho materials

### Components
- `DataTable` - Hiển thị danh sách
- `FormField` - Field form
- `Money` - Hiển thị tiền tệ
- PrimeVue components: `Drawer`, `InputText`, `InputNumber`, `Textarea`, `SelectButton`, `Tag`

### API
- Material types từ `@/api/bcare-types-v2`
- Material API functions từ `@/api/bcare-v2`
- Material constants từ `@/api/material`

## Validation Rules

### MaterialFormDrawer
- `name`: Bắt buộc - Tên nguyên liệu
- `unit`: Bắt buộc - Đơn vị tính

## Status Options
- `1`: Hoạt động (Active)
- `0`: Không hoạt động (Inactive)

## Các trường dữ liệu

### Material Object
```typescript
interface Material {
  id: number
  code?: string
  name: string
  unit: string
  packaging_specification?: string
  description?: string
  cost_price?: number
  status: number
  version: number
  created_at: string
  updated_at: string
  deleted_at: string
}
```

### MaterialAddRequest
```typescript
interface MaterialAddRequest {
  code?: string
  name: string
  unit: string
  packaging_specification?: string
  description?: string
  cost_price?: number
  status?: number
}
```
