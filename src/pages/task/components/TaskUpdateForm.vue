<template>
  <Drawer
    v-model:visible="isVisible"
    :style="{ width: '35vw' }"
    @hide="handleClose"
    position="right"
    blockScroll
    :showCloseIcon="false"
    class="flex flex-col"
  >
    <template #container>
      <!-- Header -->
      <div class="flex w-full items-center justify-between gap-10 border-b border-gray-200 p-3">
        <h3 class="flex items-center gap-2 text-lg font-semibold">
          {{ activeMenuItem === "details" ? "Cập nhật công việc" : "Hoạt động" }}
          <template v-if="activeMenuItem === 'details'">
            <i
              v-if="taskData?.task_recurring_id"
              v-tooltip.right="
                taskData.serial > 1
                  ? `Công việc lặp lại lần thứ ${taskData.serial}`
                  : 'Công việc lặp lại'
              "
              class="pi pi-refresh text-medium text-blue-500"
            />
            <i v-else class="pi pi-list text-medium text-blue-500" />
          </template>
          <i v-else class="pi pi-comments text-medium text-blue-500" />
        </h3>
        <div class="flex items-center gap-2" v-if="activeMenuItem === 'details'">
          <StateSelectBtn
            v-model:state="formData.state"
            @update:state="handleStateChange"
            editable
            :taskOwnerId="formData.creator_id"
            :primaryAssigneeIds="taskStore.getPrimaryUserIdsForTask(taskData)"
          />
        </div>
      </div>

      <!-- Main content -->
      <div class="flex flex-grow overflow-hidden">
        <!-- Left content area -->
        <div class="flex-grow overflow-y-auto" :class="{ 'p-4': activeMenuItem !== 'notes' }">
          <!-- Task Details -->
          <div v-if="activeMenuItem === 'details'" class="space-y-4">
            <FormField label="Tiêu đề" icon="pi pi-list" :readonly="!canEditTaskDetails">
              <InputText
                v-model="formData.title"
                class="w-full"
                placeholder="Nhập tiêu đề công việc"
              />
              <small class="text-red-500" v-if="errors.title">
                {{ errors.title }}
              </small>
            </FormField>

            <FormField
              label="Người nhận việc"
              icon="pi pi-user-plus"
              :readonly="!canEditTaskDetails"
            >
              <UserMultiAssign v-model="primary" usePrimeVueInput only-current-department />
              <small class="text-red-500" v-if="errors.primary">
                {{ errors.primary }}
              </small>
            </FormField>

            <FormField label="Khách hàng" icon="pi pi-user" :readonly="!canEditTaskDetails">
              <SearchPeople v-model="formData.person_id" placeholder="Tìm kiếm khách hàng" />
            </FormField>

            <div class="flex flex-wrap gap-4">
              <FormField
                label="Loại công việc"
                icon="pi pi-tag"
                class="min-w-[200px] flex-1"
                :readonly="!canEditTaskDetails"
              >
                <TaskTypeSelect v-model="formData.type!" />
              </FormField>

              <FormField
                label="Độ ưu tiên"
                icon="pi pi-flag"
                class="min-w-[200px] flex-1"
                :readonly="!canEditTaskDetails"
              >
                <PrioritySelect v-model:modelValue="formData.priority" />
              </FormField>
            </div>

            <div class="flex flex-wrap gap-4">
              <FormField
                label="Ngày bắt đầu"
                icon="pi pi-calendar-plus"
                class="min-w-[200px] flex-1"
                :readonly="!canEditTaskDetails"
              >
                <DatePicker
                  v-model="startDate"
                  showTime
                  hourFormat="24"
                  placeholder="Bắt đầu"
                  date-format="dd/mm/yy"
                  fluid
                  :minDate="new Date()"
                  @update:model-value="handleStartDateUpdate"
                />
              </FormField>
              <FormField
                label="Ngày kết thúc"
                icon="pi pi-calendar-times"
                class="min-w-[200px] flex-1"
                :readonly="!canEditTaskDetails"
              >
                <DatePicker
                  v-model="dueDate"
                  showTime
                  hourFormat="24"
                  placeholder="Kết thúc"
                  date-format="dd/mm/yy"
                  fluid
                  :minDate="new Date()"
                  @update:model-value="handleDueDateUpdate"
                />
                <small class="text-red-500" v-if="errors.due_date">
                  {{ errors.due_date }}
                </small>
              </FormField>
            </div>

            <div class="flex flex-wrap gap-4">
              <FormField
                label="Người liên quan"
                icon="pi pi-users"
                class="flex-1"
                :readonly="!canEditTaskDetails"
              >
                <UserMultiAssign :max-display="4" v-model="contributor" usePrimeVueInput />
              </FormField>

              <FormField
                label="Người duyệt"
                icon="pi pi-user-edit"
                class="flex-1"
                :readonly="!canEditTaskDetails"
              >
                <UserMultiAssign :max-display="4" v-model="reviewer" usePrimeVueInput />
              </FormField>
            </div>

            <FormField label="Nội dung công việc" icon="pi pi-clipboard">
              <NoteEditor v-model="formData.note" placeholder="Thêm nội dung" />
            </FormField>
          </div>

          <!-- Task Notes -->
          <div v-else-if="activeMenuItem === 'notes'" class="h-full">
            <TaskNoteForm
              :task-id="taskData?.id"
              :history="formData.history"
              :creator-id="taskData?.creator_id"
              :created-at="taskData?.created_at"
            />
          </div>
        </div>

        <!-- Right sidebar menu -->
        <div class="w-12 flex-shrink-0 border-l border-gray-200">
          <ul class="flex flex-col items-center py-2">
            <li v-for="item in menuItems" :key="item.key" class="mb-2 flex w-full justify-center">
              <Button
                v-tooltip.left="item.label"
                :icon="item.icon"
                :class="[
                  'flex h-10 w-10 items-center justify-center transition-colors duration-200',
                  activeMenuItem === item.key
                    ? 'bg-blue-50 text-blue-500 hover:bg-blue-100'
                    : 'text-gray-400 hover:bg-gray-100',
                ]"
                :aria-label="item.label"
                @click="activeMenuItem = item.key"
                text
                rounded
              />
            </li>
          </ul>
        </div>
      </div>

      <!-- Footer -->
      <div
        class="flex justify-end gap-2 border-t border-gray-200 p-3"
        v-if="activeMenuItem === 'details'"
      >
        <Button label="Hủy" icon="pi pi-times" @click="handleClose" severity="danger" outlined />
        <Button label="Lưu" icon="pi pi-save" @click="handleSubmit" autofocus />
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { useToggle } from "@vueuse/core";
import { cloneDeep } from "lodash";
import MultiSelect from "primevue/multiselect";
import { computed, reactive, ref, watch } from "vue";

import { TaskStateEnum } from "@/api/bcare-enum";
import {
  DepartmentResponse,
  TaskResponse,
  TaskUpdateRequest,
  UserResponse,
  UserShort,
} from "@/api/bcare-types-v2";
import { FormField } from "@/components/Form";
import SearchPeople from "@/components/Person/SearchPeople.vue";
import { PrioritySelect, StateSelectBtn } from "@/components/Select";
import { UserMultiAssign } from "@/components/User";
import { NoteEditor } from "@/components/WysiwgEditor";
import { useDatePicker } from "@/composables/useDatePicker";
import { rules, useFormValidation } from "@/composables/useFormValidation";
import { useModifiedFields } from "@/composables/useModifiedFields";
import useDepartment from "@/hooks/useDepartment";
import { useNotiStore } from "@/stores/notification";
import { useTaskStore } from "@/stores/task-store-v2";

import TaskNoteForm from "./TaskNoteForm.vue";
import TaskTypeSelect from "./TaskTypeSelect.vue";

type TaskFormData = TaskResponse;

const props = defineProps<{
  taskDataProp: TaskResponse;
}>();

const taskData = computed(() => props.taskDataProp);
const taskStore = useTaskStore();

// Use a reactive object to store the form data
const formData = reactive<TaskFormData>(getInitialFormData());

// Function to initialize or reset form data
function getInitialFormData(): TaskFormData {
  return cloneDeep({
    ...taskData.value,
    person_id: taskData.value?.person_id ?? null,
    priority: Number(taskData.value?.priority),
    note: taskData.value?.note || "",
  });
}

const { modified, track, reset } = useModifiedFields(formData);

const emit = defineEmits<{
  (e: "reload-data"): void;
  (e: "close"): void;
}>();

// Change to use ref instead of useToggle since we want to control visibility externally
const isVisible = ref(true);

const primary = ref<UserResponse[] | UserShort[]>([]);
const contributor = ref<UserResponse[] | UserShort[]>([]);
const reviewer = ref<UserResponse[] | UserShort[]>([]);

const { success } = useNotiStore();

const menuItems = [
  { key: "details", label: "Chi tiết", icon: "pi pi-list" },
  { key: "notes", label: "Hoạt động", icon: "pi pi-comments" },
];

const activeMenuItem = ref("details");

const { dateValue: startDate, handleDateUpdate: handleStartDateUpdate } = useDatePicker(
  () => formData.start_date,
  (value) => (formData.start_date = value ?? ""),
);

const { dateValue: dueDate, handleDateUpdate: handleDueDateUpdate } = useDatePicker(
  () => formData.due_date,
  (value) => (formData.due_date = value ?? ""),
);

const { departments } = useDepartment({ autoLoad: true });
const selectedDepartments = ref<DepartmentResponse[]>([]);

// Replace canEditTaskDetails with store function
const canEditTaskDetails = computed(() => {
  return taskStore.hasAdministrativePrivileges() || taskStore.isTaskCreator(formData);
});

// Update handleClose to emit close event
function handleClose() {
  clearErrors();
  reset();
  primary.value = [];
  contributor.value = [];
  reviewer.value = [];
  selectedDepartments.value = [];
  emit("close");
}

function getUsersByRole(role: string): UserResponse[] | UserShort[] {
  if (!taskData.value?.assignments) return [];
  return taskData.value.assignments
    .filter((a) => a.role === role)
    .map((a) => a.user)
    .filter((user): user is UserResponse | UserShort => user !== undefined);
}

interface TaskFormValidation {
  title: string;
  primary: UserShort[];
}

// Define validation rules
const validationRules = {
  title: [rules.required("Vui lòng nhập tiêu đề")],
  primary: [rules.required("Vui lòng chọn người nhận việc")],
};

const { errors, validateForm, clearErrors } =
  useFormValidation<TaskFormValidation>(validationRules);

// Modify handleSubmit to include validation
async function handleSubmit() {
  const formValues = {
    title: formData.title,
    primary: primary.value,
  };

  // Add date validation
  const dateValidation = rules.dateComparison(
    new Date(startDate.value || ""),
    new Date(dueDate.value || ""),
    "Thời gian kết thúc phải sau thời gian bắt đầu",
  );

  if (!validateForm(formValues) || !dateValidation.validate()) {
    if (!dateValidation.validate()) {
      errors.value.due_date = dateValidation.message;
    }
    return;
  }

  const payload: TaskUpdateRequest = {
    ...formData,
    users: [
      ...primary.value.map((user) => ({ user_id: user.id, role: "primary" as const })),
      ...contributor.value.map((user) => ({ user_id: user.id, role: "contributor" as const })),
      ...reviewer.value.map((user) => ({ user_id: user.id, role: "reviewer" as const })),
    ],
    departments: selectedDepartments.value.map((dept) => ({
      department_id: dept.id,
      role: "primary",
    })),
    start_date: startDate.value as unknown as string,
    due_date: dueDate.value as unknown as string,
    end_date: dueDate.value as unknown as string,
    modified: modified.value,
  };

  const response = await taskStore.updateTask(payload);

  if (response) {
    success({ message: "Cập nhật công việc thành công" });
    handleClose();
    emit("reload-data");
  }
}

async function handleStateChange(newState: TaskStateEnum) {
  formData.state = newState;
  await taskStore.updateTask({ state: newState, id: taskData.value.id, modified: ["state"] });
  emit("reload-data");
}

// Update watch to not depend on isVisible
watch(
  () => props.taskDataProp,
  () => {
    Object.assign(formData, getInitialFormData());
    // Reset user assignments
    primary.value = getUsersByRole("primary");
    contributor.value = getUsersByRole("contributor");
    reviewer.value = getUsersByRole("reviewer");
    // Initialize departments
    selectedDepartments.value =
      props.taskDataProp?.department_assignments
        ?.map((assignment) =>
          departments.value.find((dept) => dept.id === assignment.department_id),
        )
        .filter((dept): dept is DepartmentResponse => dept !== undefined) || [];
    track();
  },
  { immediate: true },
);
</script>
