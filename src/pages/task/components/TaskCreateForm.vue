<template>
  <div>
    <slot name="trigger" v-bind="{ open }" />
    <Drawer
      v-model:visible="isVisible"
      :style="{ width: '35vw' }"
      @hide="handleClose"
      position="right"
      blockScroll
      :showCloseIcon="false"
      class="flex flex-col"
    >
      <template #container>
        <!-- Header -->
        <div class="flex w-full items-center justify-between gap-10 border-b border-gray-200 p-3">
          <h3 class="text-lg font-semibold">Tạo công việc mới</h3>
          <!-- <div class="flex items-center gap-2">
            <StateSelectBtn v-model:state="TaskStateEnum.NEW_TASK" editable />
          </div> -->
        </div>

        <!-- Main content -->
        <div class="flex-grow overflow-y-auto p-4">
          <div class="space-y-4">
            <FormField label="Tiêu đề" icon="pi pi-list">
              <InputText
                v-model="formData.title"
                class="w-full"
                placeholder="Nhập tiêu đề công việc"
              />
              <small class="text-red-500" v-if="errors.title">
                {{ errors.title }}
              </small>
            </FormField>

            <FormField label="Người nhận việc" icon="pi pi-user-plus">
              <UserMultiAssign
                v-model="primary"
                usePrimeVueInput
                @update:modelValue="handleUserAssignmentChange"
                only-current-department
              />
              <small class="text-red-500" v-if="errors.primary">
                {{ errors.primary }}
              </small>
            </FormField>

            <FormField label="Khách hàng" icon="pi pi-user">
              <SearchPeople v-model="formData.person_id" placeholder="Tìm kiếm khách hàng" />
            </FormField>

            <div class="flex flex-wrap gap-4">
              <FormField label="Loại công việc" icon="pi pi-tag" class="min-w-[200px] flex-1">
                <TaskTypeSelect v-model="formData.type!" />
              </FormField>

              <FormField label="Độ ưu tiên" icon="pi pi-flag" class="min-w-[200px] flex-1">
                <PrioritySelect v-model:modelValue="formData.priority" />
              </FormField>
            </div>

            <div class="flex flex-wrap gap-4">
              <FormField
                label="Ngày bắt đầu"
                icon="pi pi-calendar-plus"
                class="min-w-[200px] flex-1"
              >
                <DatePicker
                  v-model="startDate"
                  showTime
                  hourFormat="24"
                  placeholder="Bắt đầu"
                  date-format="dd/mm/yy"
                  fluid
                  :minDate="new Date()"
                  @update:model-value="handleStartDateUpdate"
                />
              </FormField>
              <FormField
                label="Ngày kết thúc"
                icon="pi pi-calendar-times"
                class="min-w-[200px] flex-1"
              >
                <DatePicker
                  v-model="dueDate"
                  showTime
                  hourFormat="24"
                  placeholder="Kết thúc"
                  date-format="dd/mm/yy"
                  fluid
                  :minDate="new Date()"
                  @update:model-value="handleDueDateUpdate"
                />
                <small class="text-red-500" v-if="errors.due_date">
                  {{ errors.due_date }}
                </small>
              </FormField>
            </div>

            <div class="flex flex-wrap gap-4">
              <FormField label="Người liên quan" icon="pi pi-users" class="flex-1">
                <UserMultiAssign
                  :max-display="4"
                  v-model="contributor"
                  @update:modelValue="handleUserAssignmentChange"
                  usePrimeVueInput
                />
              </FormField>

              <FormField label="Người duyệt" icon="pi pi-user-edit" class="flex-1">
                <UserMultiAssign
                  :max-display="4"
                  v-model="reviewer"
                  @update:modelValue="handleUserAssignmentChange"
                  usePrimeVueInput
                />
              </FormField>
            </div>

            <FormField label="Nội dung công việc" icon="pi pi-clipboard">
              <NoteEditor v-model="formData.note" placeholder="Thêm nội dung" />
            </FormField>

            <FormField label="Công việc lặp lại" icon="pi pi-refresh">
              <TaskRecurrenceInput v-model="formData.cron_expression" />
            </FormField>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex justify-end gap-2 border-t border-gray-200 p-3">
          <Button label="Hủy" icon="pi pi-times" @click="handleClose" severity="danger" outlined />
          <Button label="Lưu" icon="pi pi-save" @click="handleSubmit" autofocus />
        </div>
      </template>
    </Drawer>
  </div>
</template>

<script setup lang="ts">
import { useDateFormat, useToggle } from "@vueuse/core";
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import { reactive, ref } from "vue";

import { TaskStateEnum } from "@/api/bcare-enum";
import { TaskAddRequest, UserShort } from "@/api/bcare-types-v2";
import { FormField } from "@/components/Form";
import SearchPeople from "@/components/Person/SearchPeople.vue";
import { PrioritySelect, StateSelectBtn } from "@/components/Select";
import { UserMultiAssign } from "@/components/User";
import { NoteEditor } from "@/components/WysiwgEditor";
import { useDatePicker } from "@/composables/useDatePicker";
import { rules, useFormValidation } from "@/composables/useFormValidation";
import { useAuthStore } from "@/stores/auth-store";
import { useNotiStore } from "@/stores/notification";
import { taskAddPayload, useTaskStore } from "@/stores/task-store";
import { TaskRecurrenceInput } from ".";

import TaskTypeSelect from "./TaskTypeSelect.vue";

interface TaskFormValidation {
  title: string;
  primary: UserShort[];
}

const props = defineProps<{
  personId?: number;
}>();

const formData = reactive<TaskAddRequest>({
  ...taskAddPayload,
  start_date: formatTodayWithHour(9),
  due_date: formatTodayWithHour(21),
  users: [],
  type: "",
  person_id: props.personId || 0, // Initialize with prop if available
});

const [isVisible, toggleVisible] = useToggle(false);

const { addTask } = useTaskStore();
const { success } = useNotiStore();

const emit = defineEmits<{
  (e: "reload-data"): void;
}>();

const primary = ref<UserShort[]>([]);
const contributor = ref<UserShort[]>([]);
const reviewer = ref<UserShort[]>([]);

// Define only the rules we need
const validationRules = {
  title: [rules.required("Vui lòng nhập tiêu đề")],
  primary: [rules.required("Vui lòng chọn người nhận việc")],
};

const { errors, validateForm, clearErrors } =
  useFormValidation<TaskFormValidation>(validationRules);

function open() {
  toggleVisible(true);
  handleDueDateUpdate(new Date(formData?.due_date ?? ""));
  handleStartDateUpdate(new Date(formData?.start_date ?? ""));

  const { currentUser } = useAuthStore();
  if (currentUser) {
    primary.value = [
      {
        id: currentUser.id,
        name: currentUser.name,
        username: currentUser.username,
        profile_image: currentUser.profile_image || "",
        department_id: currentUser.department_id || 0,
      },
    ];
    handleUserAssignmentChange();
  }
}

const { dateValue: startDate, handleDateUpdate: handleStartDateUpdate } = useDatePicker(
  () => formData.start_date,
  (value) => (formData.start_date = value ?? ""),
);

const { dateValue: dueDate, handleDateUpdate: handleDueDateUpdate } = useDatePicker(
  () => formData.due_date,
  (value) => (formData.due_date = value ?? ""),
);

function handleClose() {
  toggleVisible(false);
  clearErrors();
  // Reset form data
  Object.assign(formData, {
    ...taskAddPayload,
    person_id: props.personId || 0,
    start_date: formatTodayWithHour(9),
    due_date: formatTodayWithHour(21),
  });
  formData.users = [];
  primary.value = [];
  contributor.value = [];
  reviewer.value = [];
}

function handleUserAssignmentChange() {
  formData.users = [
    ...primary.value.map((user) => ({ user_id: user.id, role: "primary" as const })),
    ...contributor.value.map((user) => ({ user_id: user.id, role: "contributor" as const })),
    ...reviewer.value.map((user) => ({ user_id: user.id, role: "reviewer" as const })),
  ];
}

function formatTodayWithHour(hour: number) {
  return useDateFormat(new Date().setHours(hour - 7, 0, 0, 0), "YYYY-MM-DDTHH:mm:ss.SSSZ").value;
}

async function handleSubmit() {
  const formValues = {
    title: formData.title,
    primary: primary.value,
  };

  // Validate dates separately
  const dateValidation = rules.dateComparison(
    new Date(startDate.value || ""),
    new Date(dueDate.value || ""),
    "Thời gian kết thúc phải sau thời gian bắt đầu",
  );

  if (!validateForm(formValues) || !dateValidation.validate()) {
    if (!dateValidation.validate()) {
      errors.value.due_date = dateValidation.message;
    }
    return;
  }

  const payload = {
    ...formData,
    start_date: startDate.value as unknown as string,
    due_date: dueDate.value as unknown as string,
    end_date: dueDate.value as unknown as string,
    state: TaskStateEnum.NEW_TASK,
  };

  const response = await addTask(payload as TaskAddRequest);

  if (response) {
    success({ message: "Thêm công việc thành công" });
    handleClose();
    emit("reload-data");
  }
}

defineExpose({ open });
</script>
