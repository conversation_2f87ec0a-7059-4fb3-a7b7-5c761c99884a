<template>
  <ContentWithFixedToolbar>
    <template #left-toolbar>
      <div class="flex space-x-3 divide-x divide-solid" role="toolbar" aria-label="Left Toolbar">
        <TaskCreateForm ref="taskCreateForm" @reload-data="refetchTasks">
          <template #trigger="{ open }">
            <ButtonPrimevue
              severity="primary"
              class="font-medium"
              @click="open"
              icon="pi pi-plus"
              v-tooltip.top="'Thêm mới'"
              label="Thêm mới"
            />
          </template>
        </TaskCreateForm>
        <div class="pl-3">
          <TaskLeftMenu
            v-model="selectedFilter"
            @reload-data="refetchTasks"
            @filter-changed="handleFilterChanged"
          />
        </div>
      </div>
    </template>

    <template #right-toolbar>
      <div class="flex flex-col-reverse items-center sm:flex-row md:ml-3">
        <div class="relative mr-3 mt-3 w-full sm:mt-0 sm:w-auto"></div>
        <PopSetting title="Tuỳ chỉnh" setting-key="dashboard" />
      </div>
    </template>

    <template #footer>
      <DataTableFooter
        :totalRecords="taskStore.totalTasks || 0"
        :page="taskStore.pagination.page"
        :rows="taskStore.pagination.pageSize"
        :rowsPerPageOptions="[30, 50, 100]"
        @pageChange="handlePageChange"
      >
        <template #bulkActions>
          <div v-if="selectedTaskIds.length" class="flex h-[40px] items-center gap-2">
            <ButtonPrimevue
              outlined
              variant="outlined"
              severity="secondary"
              size="small"
              class="h-7 rounded-full px-3 text-sm"
              @click="selectedTasks = []"
            >
              <i class="pi pi-times mr-1 text-xs"></i>
              Đã chọn: {{ selectedTaskIds.length }}
            </ButtonPrimevue>

            <StateSelectBtn
              :use-custom-trigger="true"
              :editable="canEditSelectedTasksStatus"
              @update:state="bulkUpdateStatusTask"
              :primary-assignee-ids="getAllPrimaryAssigneeIds"
            >
              <template #trigger="{ toggle }">
                <ButtonPrimevue
                  icon="pi pi-pencil"
                  variant="outlined"
                  rounded
                  size="small"
                  class="h-7 w-7 p-0"
                  @click="toggle"
                  :disabled="!canEditSelectedTasksStatus"
                  v-tooltip.top="'Chỉnh sửa trạng thái công việc'"
                />
              </template>
            </StateSelectBtn>

            <ButtonPrimevue
              icon="pi pi-user-plus"
              variant="outlined"
              rounded
              size="small"
              class="h-7 w-7 p-0"
              @click="openBulkUpdateUsersModal"
              :disabled="!canModifyTaskDetails"
              v-tooltip.top="'Giao công việc'"
            />

            <ButtonPrimevue
              icon="pi pi-clock"
              variant="outlined"
              rounded
              size="small"
              class="h-7 w-7 p-0"
              @click="openBulkUpdateTimeModal"
              :disabled="!canModifyTaskDetails"
              v-tooltip.top="'Chỉnh sửa thời gian'"
            />

            <ButtonPrimevue
              v-if="canDeleteTasks"
              icon="pi pi-trash"
              severity="danger"
              rounded
              size="small"
              class="h-7 w-7 p-0"
              @click="bulkDeleteTasks"
              v-tooltip.top="'Xoá công việc'"
            />
          </div>
          <div v-else class="flex h-[40px] items-center"></div>
        </template>
      </DataTableFooter>
    </template>

    <DataTable
      :columns="columns"
      :data="taskStore.tasks"
      :total-records="taskStore.totalTasks || 0"
      :loading="taskStore.loading"
      :paginator="false"
      v-model:selected-items="selectedTasks"
      v-model:filters="filters"
      :show-header="false"
      :hide-header-row="true"
      :custom-paginator="true"
      checkbox
      :rows="taskStore.pagination.pageSize"
      :rows-per-page-options="[30, 50, 100]"
      size="small"
      class="h-full"
    >
      <template #title="{ data, index }">
        <div class="flex items-center gap-2" @click="openTaskForm(data)">
          <span class="font-medium">{{ index + 1 }}.</span>
          <PriorityChip :priority="Number(data.priority)" :text="data.title" />

          <!-- Task Type Tag -->
          <TaskTypeTag v-if="data.type" :type="data.type" />

          <i
            v-if="data.task_recurring_id"
            class="pi pi-refresh text-blue-500"
            v-tooltip="
              data.serial > 1 ? `Công việc lặp lại lần thứ ${data.serial}` : 'Công việc lặp lại'
            "
          />
        </div>
      </template>

      <template #person_id="{ data }">
        <PersonCard :person="data.person" submit-type="new-tab" />
      </template>

      <template #start_date="{ data }">
        <DateTimeInfo :date="data.start_date" />
      </template>

      <template #due_date="{ data }">
        <div class="flex flex-wrap items-center gap-2">
          <DateTimeInfo
            :date="data.due_date"
            :text-color="
              calculateLateDays(data.due_date, data.state, data.completed_at) > 0
                ? 'text-red-500'
                : undefined
            "
            v-tooltip="{
              value: `Chậm ${calculateLateDays(data.due_date, data.state, data.completed_at)} ngày`,
              disabled: calculateLateDays(data.due_date, data.state, data.completed_at) <= 0,
            }"
          />
        </div>
      </template>

      <template #assignments.filter="{ filterModel, filterCallback }">
        <div class="min-w-[180px]">
          <div class="p-column-filter flex items-center gap-2">
            <UserMultiAssign
              use-prime-vue-input
              :modelValue="assignmentsFilter"
              @update:modelValue="handleAssignmentsFilterChange"
              show-inactive-users
              :max-display="2"
            />
            <div v-if="assignmentsFilter.length">
              <ButtonPrimevue
                icon="pi pi-filter-slash"
                class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button"
                @click="clearAssignmentsFilter"
              />
            </div>
          </div>
        </div>
      </template>

      <template #assignments="{ data }">
        <UserAvatarGroup
          :users="getUserGroup(data.assignments)"
          :max-display="3"
          :expand="true"
          :expanded-spacing="1"
        />
      </template>

      <template #state="{ data }">
        <StateSelectBtn
          :state="data.state"
          @update:state="(newState) => taskStore.updateTaskState(data.id, newState)"
          editable
          :taskOwnerId="data.creator_id"
          :primaryAssigneeIds="taskStore.getPrimaryUserIdsForTask(data)"
        />
      </template>

      <template #state.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <StateSelectBtn
            isFilter
            :state="stateFilter || undefined"
            @filter="handleStateFilter"
            size="small"
            editable
          />
          <div v-if="stateFilter">
            <ButtonPrimevue
              icon="pi pi-filter-slash"
              class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button"
              @click="clearStateFilter"
            />
          </div>
        </div>
      </template>

      <template #actions="{ data }">
        <div class="flex justify-center">
          <ButtonPrimevue
            v-if="taskStore.canUserDeleteTask(data)"
            type="button"
            icon="pi pi-ellipsis-v"
            class="p-button-text p-button-rounded p-button-plain h-7 w-7"
            @click="(event) => toggleActionMenu(event, data)"
            aria-haspopup="true"
          />
          <Menu
            :ref="(el) => setActionMenuRef(el, data.id)"
            :id="`task_menu_${data.id}`"
            :model="getTaskActionItems(data)"
            :popup="true"
          />
        </div>
      </template>
    </DataTable>

    <TaskBulkUpdateUsersModal
      v-model:visible="isBulkUpdateUsersModalVisible"
      :task-ids="selectedTaskIds"
      @close="handleModalClose"
      @updated="refetchTasks"
    />

    <TaskBulkUpdateTimeModal
      v-model:visible="isBulkUpdateTimeModalVisible"
      :task-ids="selectedTaskIds"
      @close="handleModalClose"
      @updated="refetchTasks"
    />

    <!-- Single instance of TaskUpdateForm -->
    <TaskUpdateForm
      v-if="selectedTask"
      :task-data-prop="selectedTask"
      :key="selectedTask.id"
      @reload-data="refetchTasks"
      @close="selectedTask = null"
    />
  </ContentWithFixedToolbar>

  <ConfirmPopup />
</template>

<script setup lang="ts">
import ButtonPrimevue from "primevue/button";
import ConfirmPopup from "primevue/confirmpopup";
import Menu from "primevue/menu";
import { MenuItem } from "primevue/menuitem";
import Paginator, { PageState } from "primevue/paginator";
import { useConfirm } from "primevue/useconfirm";

import { TaskStateEnum } from "@/api/bcare-enum";
import type { TaskAssignmentResponse, TaskResponse, User, UserShort } from "@/api/bcare-types-v2";
import { PriorityChip } from "@/components/Chip";
import { DataTable } from "@/components/DataTable";
import DataTableFooter from "@/components/DataTable/DataTableFooter.vue";
import { DateTimeInfo } from "@/components/InfoText";
import PopSetting from "@/components/Settings/PopSetting.vue";
import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";
import UserMultiAssign from "@/components/User/UserMultiAssign.vue";
import {
  createDefaultTaskFilters,
  taskColumns,
  TaskFilters,
} from "@/constants/columns/task-columns";
import ContentWithFixedToolbar from "@/layouts/ContentWithFixedToolbar.vue";
import { useAuthStore } from "@/stores/auth-store";
import { useTaskStore } from "@/stores/task-store-v2";
import { calculateLateDays } from "@/utils/time-helper";

import { TaskCreateForm, TaskLeftMenu, TaskUpdateForm } from "./components";
import TaskBulkUpdateTimeModal from "./components/TaskBulkUpdateTimeModal.vue";
import TaskBulkUpdateUsersModal from "./components/TaskBulkUpdateUsersModal.vue";
import TaskTypeTag from "./components/TaskTypeTag.vue";
import { TASK_VIEW_TYPES, type TaskViewType } from "./constants";

// Initialize stores
const taskStore = useTaskStore();
const confirm = useConfirm();
const authStore = useAuthStore();

// State management
const selectedTasks = ref<TaskResponse[]>([]);
const selectedTaskIds = computed<number[]>(() => selectedTasks.value.map((task) => task.id));
const selectedFilter = ref<TaskViewType>(TASK_VIEW_TYPES.ALL);
const selectedTask = ref<TaskResponse | null>(null);
const taskCreateForm = ref();

// Assignments filter handling
const assignmentsFilter = ref<User[]>([]);

// DataTable filters
const filters = ref<TaskFilters>(createDefaultTaskFilters());

// Sử dụng columns từ file config
const columns = taskColumns;

// Modal visibility state using useToggle for better performance
const [isBulkUpdateUsersModalVisible, toggleBulkUpdateUsersModal] = useToggle(false);
const [isBulkUpdateTimeModalVisible, toggleBulkUpdateTimeModal] = useToggle(false);

// Replace these computed properties with store functions
const canDeleteTasks = computed(() => taskStore.canUserDeleteMultipleTasks());

// Get all primary assignee IDs from selected tasks
const getAllPrimaryAssigneeIds = computed<number[]>(() =>
  taskStore.getAllPrimaryAssigneeIdsFromTasks(selectedTasks.value),
);

// Check if current user can edit status of selected tasks
const canEditSelectedTasksStatus = computed(() =>
  taskStore.canUserEditMultipleTasksStatus(selectedTasks.value),
);

// Single computed property for creator-only permissions
const canModifyTaskDetails = computed(() => {
  return taskStore.hasCreatorOnlyPermissionForMultiple(selectedTasks.value);
});

// Watch for filter changes with debounce for better performance
watch(
  filters,
  (newFilters) => {
    taskStore.applyDataTableFilters(newFilters);
  },
  { deep: true },
);

// Helper functions
const getUserGroup = (assignments?: TaskAssignmentResponse[]): UserShort[] => {
  return taskStore.getPrimaryUsersFromTask(assignments);
};

// Event handlers
const handlePageChange = (event: PageState) => {
  const newPage = event.page + 1;
  if (taskStore.pagination.pageSize !== event.rows) {
    taskStore.pagination.setPageSize(event.rows);
  } else {
    taskStore.pagination.goToPage(newPage);
  }
};

const handleAssignmentsFilterChange = (users: User[]) => {
  assignmentsFilter.value = users;
  taskStore.applyAssignmentsFilter(users.map((user) => user.id));
};

// Filter functions
const clearAssignmentsFilter = () => {
  assignmentsFilter.value = [];
  taskStore.applyAssignmentsFilter([]);
};

// Task operations
const bulkDeleteTasks = () => {
  taskStore.deleteMultipleTasksWithConfirmation(selectedTaskIds.value, confirm);
};

const bulkUpdateStatusTask = async (newState: TaskStateEnum) => {
  await taskStore.updateMultipleTasksStatus(selectedTaskIds.value, newState);
};

// Modal functions
const openBulkUpdateUsersModal = () => {
  toggleBulkUpdateUsersModal(true);
};

const openBulkUpdateTimeModal = () => {
  toggleBulkUpdateTimeModal(true);
};

const handleModalClose = () => {
  toggleBulkUpdateUsersModal(false);
  toggleBulkUpdateTimeModal(false);
};

const openTaskForm = (task: TaskResponse) => {
  selectedTask.value = task;
};

// Data operations
const refetchTasks = () => {
  taskStore.refresh();
  selectedTasks.value = [];
};

// Initialize data on component mount - kiểm tra trước khi load
onMounted(() => {
  // Chỉ load dữ liệu nếu chưa có
  if (taskStore.tasks.length === 0) {
    taskStore.loadInitialData();
  }
});

// Xử lý khi filter thay đổi
const handleFilterChanged = (filterType: TaskViewType) => {
  const userId = parseInt((authStore.user?.user.id ?? 0).toString());
  taskStore.applyFilterByViewType(filterType, userId);
};

// Add these refs and functions
const actionMenuRefs = ref(new Map());

const setActionMenuRef = (el: any, id: number) => {
  if (el) {
    actionMenuRefs.value.set(id, el);
  }
};

const toggleActionMenu = (event: Event, data: TaskResponse) => {
  const menuRef = actionMenuRefs.value.get(data.id);
  if (menuRef) {
    menuRef.toggle(event);
  }
  event.stopPropagation();
};

const getTaskActionItems = (task: TaskResponse): MenuItem[] => {
  const items: MenuItem[] = [];

  // Only show delete option if user has permission
  if (taskStore.canUserDeleteTask(task)) {
    items.push({
      label: "Xóa",
      icon: "pi pi-trash",
      className: "text-red-500",
      command: () => taskStore.deleteTaskWithConfirmation(task, confirm),
    });
  }

  return items;
};

// Clean up when component unmounts
onBeforeUnmount(() => {
  taskStore.$dispose();
});

// Add this with other refs at the top of the script
const stateFilter = ref<TaskStateEnum | null>(null);

// Add this with other event handlers
const handleStateFilter = (state: TaskStateEnum | null) => {
  stateFilter.value = state;
  taskStore.applyStateFilter(state);
};

const clearStateFilter = () => {
  stateFilter.value = null;
  taskStore.applyStateFilter(null);
};
</script>
