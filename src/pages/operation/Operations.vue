<template>
  <div class="intro-y mt-8 flex items-center">
    <h2 class="mr-auto text-lg font-medium"><PERSON><PERSON><PERSON>n lý thao tác</h2>
  </div>

  <div class="intro-y mt-5">
    <DataTable
      title="Danh sách thao tác"
      :columns="columns"
      :data="tableState.operations"
      :loading="false"
      :total-records="tableState.total"
      paginator
      :rows="perPage"
      v-model:filters="filters"
      @page="handlePageChange"
      :show-actions="{ edit: true, delete: true }"
      @on-edit="handleEdit"
      @on-delete="handleDelete"
      size="small"
      striped-rows
    >
      <template #left-header>
        <div class="flex items-center gap-2">
          <span class="text-base font-medium"> Danh sách thao tác ({{ tableState.total }}) </span>
        </div>
      </template>

      <template #right-header>
        <Button severity="primary" icon="pi pi-plus" label="Thêm thao tác" @click="handleCreate" />
      </template>

      <template #name="{ data }">
        <div class="flex items-center gap-2">
          <i class="pi pi-cog text-blue-500"></i>
          <span class="font-medium">{{ data.name }}</span>
        </div>
      </template>

      <template #group="{ data }">
        <div class="flex flex-wrap gap-1">
          <Tag v-for="group in data.group" :key="group" :value="group" severity="info" />
        </div>
      </template>

      <template #duration="{ data }">
        <div class="flex items-center gap-1">
          <i class="pi pi-clock text-gray-500"></i>
          <span>{{ data.duration }} phút</span>
        </div>
      </template>

      <template #status="{ data }">
        <Tag
          :value="data.status === 2 ? 'Hoạt động' : 'Không hoạt động'"
          :severity="data.status === 2 ? 'success' : 'danger'"
        />
      </template>

      <template #created_at="{ data }">
        <DateTime :value="data.created_at" />
      </template>
    </DataTable>
  </div>

  <OperationFormDrawer
    v-model:visible="showOperationForm"
    :operation-id="selectedOperationId"
    @success="handleFormSuccess"
  />
</template>

<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import { computed, onMounted, ref, watch } from "vue";

import type { OperationResponse, OperationListResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import { type ColumnDefinition, DataTable } from "@/components/DataTable";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { useFilterList } from "@/hooks/useFilterList";
import useOperation from "@/hooks/useOperation";

import OperationFormDrawer from "./components/OperationFormDrawer.vue";

const { listOperations, deleteOperation } = useOperation({ autoLoad: false });

// State
const tableState = ref<OperationListResponse>({
  total: 0,
  total_page: 0,
  operations: [],
});
const perPage = ref<number>(10);
const currentPage = ref(1);

// Filters
const filters = ref({
  global: { value: null, matchMode: FilterMatchMode.CONTAINS },
  name: { value: null, matchMode: FilterMatchMode.CONTAINS },
  group: { value: null, matchMode: FilterMatchMode.CONTAINS },
  status: { value: null, matchMode: FilterMatchMode.EQUALS },
});

// Columns definition
const columns: ColumnDefinition[] = [
  {
    field: "name",
    header: "Tên thao tác",
    sortable: true,
    filterable: true,
    filterType: "text",
  },
  {
    field: "group",
    header: "Nhóm",
    sortable: false,
    filterable: true,
    filterType: "text",
  },
  {
    field: "duration",
    header: "Thời gian",
    sortable: true,
    filterable: false,
  },
  {
    field: "status",
    header: "Trạng thái",
    sortable: true,
    filterable: true,
    filterType: "select",
    filterOptions: [
      { label: "Hoạt động", value: 2 },
      { label: "Không hoạt động", value: 1 },
    ],
  },
  {
    field: "created_at",
    header: "Ngày tạo",
    sortable: true,
    filterable: false,
  },
];

// Filter list hook
const {
  currentFilterPayload,
  handlePageChange: handlePageChangeBase,
  handlePerPageChange,
} = useFilterList({
  filters,
  currentPage,
  perPage,
  loadList,
});

const handlePageChange = (event: any) => {
  currentPage.value = event.page + 1;
  handlePageChangeBase(event);
};

const loadList = async (filters: Record<string, any>) => {
  try {
    const response = await listOperations({
      ...filters,
      page: currentPage.value,
      page_size: perPage.value,
    });
    if (response) {
      tableState.value = response;
    }
  } catch (error) {
    console.error("Error loading operations:", error);
  }
};

const { confirm } = useConfirmTippy();

const handleDelete = async (operation: OperationResponse, e?: MouseEvent) => {
  confirm(e, {
    title: "Xác nhận xóa thao tác",
    icon: "pi pi-trash",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      await deleteOperation({ id: operation.id });
      loadList(currentFilterPayload.value);
    },
  });
};

// Lifecycle
onMounted(() => {
  // Không cần gọi loadList ở đây
});

// Watchers
watch(perPage, (newPerPage) => {
  handlePerPageChange(newPerPage);
});

// Thêm watcher để theo dõi sự thay đổi của filters
watch(
  filters,
  () => {
    // Khi filters thay đổi, reset về trang 1
    currentPage.value = 1;
  },
  { deep: true },
);

const showOperationForm = ref(false);
const selectedOperationId = ref<number>();

const handleCreate = () => {
  selectedOperationId.value = undefined;
  showOperationForm.value = true;
};

const handleEdit = (operation: OperationResponse) => {
  selectedOperationId.value = operation.id;
  showOperationForm.value = true;
};

const handleFormSuccess = () => {
  loadList(currentFilterPayload.value);
};
</script>
