<template>
  <div class="intro-y mt-8 flex items-center">
    <h2 class="mr-auto text-lg font-medium">Q<PERSON>ản lý nội dung điều trị</h2>
  </div>

  <div class="intro-y mt-5">
    <DataTable
      title="Danh sách nội dung điều trị"
      :columns="columns"
      :data="tableState.operations"
      :loading="false"
      :total-records="tableState.total"
      paginator
      :rows="perPage"
      v-model:filters="filters"
      @page="handlePageChange"
      :show-actions="{ edit: true, delete: true }"
      @on-edit="handleEdit"
      @on-delete="handleDelete"
      size="small"
      striped-rows
    >
      <template #left-header>
        <div class="flex items-center gap-2">
          <span class="text-base font-medium"> Danh sách nội dung điều trị ({{ tableState.total }}) </span>
        </div>
      </template>

      <template #right-header>
        <Button severity="primary" icon="pi pi-plus" label="Thêm nội dung điều trị" @click="handleCreate" />
      </template>

      <template #name="{ data }">
        <div class="flex items-center gap-2">
          <i class="pi pi-cog text-blue-500"></i>
          <span class="font-medium">{{ data.name }}</span>
        </div>
      </template>

      <template #group="{ data }">
        <div class="flex flex-wrap gap-1">
          <Tag v-for="group in data.group" :key="group" :value="group" severity="info" />
        </div>
      </template>

      <template #duration="{ data }">
        <div class="flex items-center gap-1">
          <i class="pi pi-clock text-gray-500"></i>
          <span>{{ data.duration }} phút</span>
        </div>
      </template>

      <template #status="{ data }">
        <Tag
          :value="data.status === 2 ? 'Hoạt động' : 'Không hoạt động'"
          :severity="data.status === 2 ? 'success' : 'danger'"
        />
      </template>

      <template #created_at="{ data }">
        <DateTime :value="data.created_at" />
      </template>
    </DataTable>
  </div>

  <OperationFormDrawer
    v-model:visible="showOperationForm"
    :operation-id="selectedOperationId"
    @success="handleFormSuccess"
  />
</template>

<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import Button from "primevue/button";
import Tag from "primevue/tag";
import { computed, onMounted, ref, watch } from "vue";

import type { OperationListResponse, OperationResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import { DataTable, type ColumnDefinition } from "@/components/DataTable";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { useFilterList } from "@/hooks/useFilterList";
import useOperation from "@/hooks/useOperation";

import OperationFormDrawer from "./components/OperationFormDrawer.vue";

const { listOperations, deleteOperation } = useOperation({ autoLoad: false });

// State
const tableState = ref<OperationListResponse>({
  total: 0,
  total_page: 0,
  operations: [],
});
const perPage = ref<number>(10);
const currentPage = ref(1);

// Columns definition
const columns = computed<ColumnDefinition[]>(() => [
  {
    field: "name",
    header: "Tên nội dung điều trị",
    filterType: "text",
    filterPlaceholder: "Tìm theo tên",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  {
    field: "group",
    header: "Nhóm",
    filterType: "text",
    filterPlaceholder: "Tìm theo nhóm",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  {
    field: "duration",
    header: "Thời gian",
    showFilterMenu: false,
  },
  {
    field: "status",
    header: "Trạng thái",
    filterType: "select",
    filterPlaceholder: "Chọn trạng thái",
    filterMatchMode: FilterMatchMode.EQUALS,
    showFilterMenu: false,
    filterOptions: [
      { title: "Hoạt động", value: 2 },
      { title: "Không hoạt động", value: 1 },
    ],
  },
  {
    field: "created_at",
    header: "Ngày tạo",
    showFilterMenu: false,
  },
]);

// Filter configurations
const filterConfigs = {
  name: { field: "search", isFilter: false },
  group: { field: "group", isFilter: true },
  status: {
    field: "status",
    isFilter: true,
    valueTransform: (value: string | number) => Number(value),
  },
};

const defaultFilters = {
  page: 1,
  page_size: perPage.value,
};

const { filters, currentFilterPayload } = useFilterList(
  (filters) => {
    loadList(filters);
  },
  filterConfigs,
  defaultFilters,
);

// Handlers
const handlePageChange = async (event: { first: number; rows: number }) => {
  try {
    const page = Math.floor(event.first / event.rows) + 1;
    currentPage.value = page;

    // Gọi trực tiếp API với page mới, không thông qua useFilterList
    const response = await listOperations({
      ...currentFilterPayload.value,
      page,
      page_size: perPage.value,
    });

    if (response) {
      tableState.value = response;
    }
  } catch (error) {
    console.error("Error changing page:", error);
  }
};

const handlePerPageChange = (newPerPage: number) => {
  perPage.value = newPerPage;
  loadList({ ...currentFilterPayload.value, page_size: newPerPage });
};

const loadList = async (filters: Record<string, any>) => {
  try {
    const response = await listOperations({
      ...filters,
      page: currentPage.value,
      page_size: perPage.value,
    });
    if (response) {
      tableState.value = response;
    }
  } catch (error) {
    console.error("Error loading operations:", error);
  }
};

const { confirm } = useConfirmTippy();

const handleDelete = async (operation: OperationResponse, e?: MouseEvent) => {
  confirm(e, {
    title: "Xác nhận xóa nội dung điều trị",
    icon: "pi pi-trash",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      await deleteOperation({ id: operation.id });
      loadList(currentFilterPayload.value);
    },
  });
};

// Lifecycle
onMounted(() => {
  // Không cần gọi loadList ở đây
});

// Watchers
watch(perPage, (newPerPage) => {
  handlePerPageChange(newPerPage);
});

// Thêm watcher để theo dõi sự thay đổi của filters
watch(
  filters,
  () => {
    // Khi filters thay đổi, reset về trang 1
    currentPage.value = 1;
  },
  { deep: true },
);

const showOperationForm = ref(false);
const selectedOperationId = ref<number>();

const handleCreate = () => {
  selectedOperationId.value = undefined;
  showOperationForm.value = true;
};

const handleEdit = (operation: OperationResponse) => {
  selectedOperationId.value = operation.id;
  showOperationForm.value = true;
};

const handleFormSuccess = () => {
  loadList(currentFilterPayload.value);
};
</script>
