<template>
  <Drawer
    :closable="false"
    :modal="true"
    :pt="{
      header: 'border-b border-gray-200 px-3 py-2',
      footer: 'px-3 py-2 border-t border-gray-200',
    }"
    :style="{ width: '50rem' }"
    :visible="visible"
    blockScroll
    position="right"
    @hide="onClose"
    @update:visible="$emit('update:visible', $event)"
  >
    <template #header>
      <span class="text-lg font-medium">
        {{ isEdit ? "Cập nhật thao tác" : "Thêm thao tác" }}
      </span>
    </template>

    <form class="p-2" @submit.prevent="onSubmit">
      <div class="space-y-6">
        <BasicInformation v-model:formData="formData" :errors="errors" />
        <Divider class="!my-6" />
        <DetailsInformation v-model:formData="formData" :errors="errors" />
      </div>
    </form>

    <template #footer>
      <div class="flex justify-end gap-3 px-3 py-2">
        <Button icon="pi pi-times" label="Hủy" outlined severity="danger" @click="onClose" />
        <Button autofocus icon="pi pi-save" label="Lưu" @click="onSubmit" />
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import Button from "primevue/button";
import Divider from "primevue/divider";
import Drawer from "primevue/drawer";
import { computed, ref, watch } from "vue";

import { OperationAddRequest } from "@/api/bcare-types-v2";
import { useFormValidation } from "@/composables/useFormValidation";
import useOperation from "@/hooks/useOperation";

import BasicInformation from "./OperationFormBasicInfo.vue";
import DetailsInformation from "./OperationFormDetails.vue";

interface Props {
  visible: boolean;
  operationId?: number;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "success"): void;
}>();

const { addOperation, updateOperation, getOperation } = useOperation();
const isEdit = computed(() => !!props.operationId);

const formData = ref<OperationAddRequest & { status?: number; description?: string }>({
  name: "",
  group: [],
  duration: 0,
  status: 2, // Default to Active
  description: "",
});

const { errors, validateForm } = useFormValidation({
  name: [
    { validate: (v) => !!v && v.length >= 2, message: "Tên thao tác phải có ít nhất 2 ký tự" },
  ],
  duration: [{ validate: (v) => v >= 0, message: "Thời gian thực hiện phải lớn hơn hoặc bằng 0" }],
  group: [{ validate: (v) => Array.isArray(v) && v.length > 0, message: "Vui lòng thêm ít nhất 1 nhóm thao tác" }],
});

const resetForm = () => {
  formData.value = {
    name: "",
    group: [],
    duration: 0,
    status: 2,
    description: "",
  };

  Object.keys(errors.value).forEach((key) => {
    errors.value[key] = "";
  });
};

watch(
  () => props.operationId,
  async (newValue) => {
    resetForm();
    if (newValue) {
      const operation = await getOperation({ id: newValue });
      if (operation) {
        const { name, group, duration, status } = operation;

        formData.value = {
          name,
          group: group || [],
          duration,
          status,
          description: "", // OperationResponse doesn't have description field
        };
      }
    }
  },
  { immediate: true },
);

watch(
  () => props.visible,
  (newValue) => {
    if (!newValue) {
      resetForm();
    }
  },
);

const onSubmit = async () => {
  if (!validateForm(formData.value)) return;

  try {
    // Create the request object with only the required fields for the API
    const requestData: OperationAddRequest = {
      name: formData.value.name,
      group: formData.value.group,
      duration: formData.value.duration,
    };

    if (isEdit.value) {
      const res = await updateOperation({ 
        id: props.operationId!, 
        ...requestData 
      });
      if (res) {
        emit("success");
        onClose();
      }
    } else {
      const res = await addOperation(requestData);
      if (res) {
        emit("success");
        onClose();
      }
    }
  } catch (error) {
    console.error(error);
  }
};

const onClose = () => {
  emit("update:visible", false);
  resetForm();
};
</script>
