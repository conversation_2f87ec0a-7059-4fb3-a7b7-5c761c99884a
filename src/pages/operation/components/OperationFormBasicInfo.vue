<template>
  <div class="space-y-4">
    <FormField label="Tên thao tác" icon="pi pi-cog">
      <InputText
        v-model="formData.name"
        placeholder="Nhập tên thao tác"
        class="w-full"
        :class="{ 'p-invalid': errors.name }"
      />
      <small class="p-error">{{ errors.name }}</small>
    </FormField>

    <FormField label="Mô tả" icon="pi pi-align-left">
      <Textarea
        v-model="formData.description"
        rows="3"
        placeholder="Nhập mô tả thao tác"
        class="w-full"
      />
    </FormField>

    <FormField label="Trạng thái" icon="pi pi-check-circle">
      <SelectButton
        v-model="formData.status"
        :options="statusOptions"
        optionLabel="label"
        optionValue="value"
        dataKey="value"
      >
        <template #option="{ option }">
          <div class="flex items-center gap-2">
            <i :class="[option.icon, option.value === 2 ? 'text-green-500' : 'text-gray-500']"></i>
            <span :class="[option.value === 2 ? 'text-green-600' : 'text-gray-600', 'font-medium']">
              {{ option.label }}
            </span>
          </div>
        </template>
      </SelectButton>
    </FormField>
  </div>
</template>

<script setup lang="ts">
import InputText from "primevue/inputtext";
import SelectButton from "primevue/selectbutton";
import Textarea from "primevue/textarea";

import type { OperationAddRequest } from "@/api/bcare-types-v2";
import FormField from "@/components/Form/FormField.vue";

interface Props {
  formData: OperationAddRequest;
  errors: Partial<Record<string, string>>;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:formData", value: OperationAddRequest): void;
}>();

const statusOptions = [
  {
    label: "Hoạt động",
    value: 2,
    icon: "pi pi-check-circle",
  },
  {
    label: "Không hoạt động",
    value: 1,
    icon: "pi pi-ban",
  },
];
</script>
