<template>
  <div class="space-y-4">
    <FormField label="Thời gian thực hiện (phút)" icon="pi pi-clock">
      <InputNumber
        v-model="formData.duration"
        :min="0"
        :step="1"
        showButtons
        class="w-full"
        placeholder="Nhập thời gian thực hiện"
        :class="{ 'p-invalid': errors.duration }"
      />
      <small class="p-error">{{ errors.duration }}</small>
    </FormField>

    <FormField label="Nhóm thao tác" icon="pi pi-tags">
      <Chips
        v-model="formData.group"
        placeholder="Nhập nhóm thao tác và nhấn Enter"
        class="w-full"
        :class="{ 'p-invalid': errors.group }"
      />
      <small class="p-error">{{ errors.group }}</small>
      <small class="text-gray-500 text-xs mt-1 block">
        Nhập tên nhóm và nhấn Enter để thêm. <PERSON><PERSON> thể thêm nhiều nhóm.
      </small>
    </FormField>

    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-start gap-3">
        <i class="pi pi-info-circle text-blue-500 mt-0.5"></i>
        <div class="text-sm text-blue-700">
          <p class="font-medium mb-1">Gợi ý về thao tác:</p>
          <ul class="list-disc list-inside space-y-1 text-xs">
            <li>Thời gian thực hiện nên được tính bằng phút</li>
            <li>Nhóm thao tác giúp phân loại và tìm kiếm dễ dàng hơn</li>
            <li>Có thể thêm nhiều nhóm cho một thao tác</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Chips from "primevue/chips";
import InputNumber from "primevue/inputnumber";

import type { OperationAddRequest } from "@/api/bcare-types-v2";
import FormField from "@/components/Form/FormField.vue";

interface Props {
  formData: OperationAddRequest & { status?: number; description?: string };
  errors: Partial<Record<string, string>>;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:formData", value: OperationAddRequest & { status?: number; description?: string }): void;
}>();
</script>
