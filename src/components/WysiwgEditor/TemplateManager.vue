<template>
  <div>
    <Dialog
      v-model:visible="visible"
      :modal="true"
      :closable="true"
      :dismissable-mask="true"
      header="Cấu hình note template"
      :style="{ minWidth: '70vw', maxWidth: '1200px' }"
      :draggable="false"
      :resizable="false"
    >
      <div class="flex flex-1 overflow-hidden">
        <!-- Template List Section (Left) -->
        <div class="flex h-auto w-1/3 flex-col border-r pr-4">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="text-lg font-semibold">Danh sách</h3>
            <div class="flex items-center gap-2">
              <Button
                icon="pi pi-plus"
                label="New Template"
                size="small"
                @click="createNewTemplate"
                :disabled="isLoading"
              />
            </div>
          </div>

          <div class="mb-3">
            <IconField class="w-full">
              <InputText
                v-model="searchQuery"
                placeholder="Search templates..."
                class="w-full"
                size="small"
              />
              <InputIcon class="pi pi-search" />
            </IconField>
          </div>

          <div class="flex-1 overflow-y-auto">
            <ProgressSpinner v-if="isLoading" class="mx-auto my-4 h-8 w-8" />

            <div v-else-if="filteredTemplates.length === 0" class="py-6 text-center text-gray-500">
              No templates found. Create your first template.
            </div>

            <div v-else class="space-y-2">
              <div
                v-for="template in filteredTemplates"
                :key="template.id"
                class="cursor-pointer rounded-lg border p-3 transition-colors hover:bg-gray-50"
                :class="{ 'border-blue-300 bg-blue-50': selectedTemplate?.id === template.id }"
                @click="selectTemplate(template)"
              >
                <div class="mb-1 truncate font-medium">
                  {{ template.description || "No description" }}
                </div>
                <div class="mb-2 flex flex-wrap gap-1">
                  <Chip
                    v-for="(keyword, index) in template.keywords"
                    :key="index"
                    :label="'/' + keyword"
                    class="bg-gray-200 text-xs text-gray-700"
                  />
                </div>
                <div class="flex items-center justify-between text-xs text-gray-500">
                  <span>ID: {{ template.id }}</span>
                  <div class="flex gap-1">
                    <i
                      @click="handleDeleteTemplate($event, template)"
                      class="pi pi-trash cursor-pointer text-danger"
                      aria-label="Delete"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Template Form Section (Right) -->
        <div class="flex h-auto w-2/3 flex-col pl-4">
          <div v-if="selectedTemplate || isEditing" class="flex-1">
            <TemplateForm
              :template="currentTemplate"
              :is-editing="isEditing"
              :is-saving="isSaving"
              @save="handleSaveTemplate"
              @cancel="resetForm"
            />
          </div>
          <div v-else class="flex flex-1 items-center justify-center text-gray-500">
            <div class="text-center">
              <i class="pi pi-file-edit mb-3 text-5xl"></i>
              <p class="mb-4">Select a template to edit or create a new one</p>
              <Button
                icon="pi pi-plus"
                label="Create New Template"
                @click="createNewTemplate"
                size="small"
                severity="primary"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- PrimeVue's ConfirmDialog is used instead of a custom component -->
    </Dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent, watch, computed } from "vue";
import { useConfirmTippy } from "@/composables/useConfirmTippy";

import { useConfigurations } from "@/hooks/useSetting";
import { useConfigurationsStore } from "@/stores/configuration-store";
import { TemplateItem, emptyTemplate } from "./types";
import { refreshTemplates } from "./template";

// Lazy load components for better performance
const TemplateForm = defineAsyncComponent(() => import("./components/TemplateForm.vue"));

// Initialize the confirm dialog
const { confirm: confirmTippy } = useConfirmTippy();

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

// Initialize state
const templates = ref<TemplateItem[]>([]);
const isLoading = ref(false);
const isSaving = ref(false);
const isEditing = ref(false);

// State management
const searchQuery = ref("");
const selectedTemplate = ref<TemplateItem | null>(null);
const currentTemplate = ref<TemplateItem>({ ...emptyTemplate });

// Get configuration hooks
const { fetchSettings, getSetting, syncSettingValue } = useConfigurations();
const configStore = useConfigurationsStore();

// Filter templates based on search query
const filteredTemplates = computed(() => {
  if (!searchQuery.value.trim()) {
    return templates.value;
  }

  const query = searchQuery.value.toLowerCase();
  return templates.value.filter((template) => {
    // Search in ID
    if (template.id.toLowerCase().includes(query)) {
      return true;
    }

    // Search in description
    if (template.description && template.description.toLowerCase().includes(query)) {
      return true;
    }

    // Search in keywords
    if (template.keywords.some((keyword) => keyword.toLowerCase().includes(query))) {
      return true;
    }

    return false;
  });
});

// Load templates from server
const loadTemplates = async () => {
  isLoading.value = true;
  try {
    // Force refresh from server
    await configStore.init({ category: "templates", name: "templates" });
    await fetchSettings("templates", "templates");
    const templatesSetting = getSetting("templates", "templates");

    if (templatesSetting?.value?.templates) {
      templates.value = templatesSetting.value.templates as TemplateItem[];
      console.log("Templates loaded from server:", templates.value);
    } else {
      templates.value = [];
      console.log("No templates found on server");
    }

    // Also refresh templates in the template plugin
    await refreshTemplates();
  } catch (error) {
    console.error("Error loading templates:", error);
  } finally {
    isLoading.value = false;
  }
};

// Select template for editing
const selectTemplate = (template: TemplateItem) => {
  selectedTemplate.value = template;
  currentTemplate.value = { ...template };
  isEditing.value = true;
};

// Handle delete template
const handleDeleteTemplate = (event: Event, template: TemplateItem) => {
  confirmTippy(event as MouseEvent, {
    title: "Xác nhận Xóa",
    icon: "pi pi-exclamation-triangle",
    acceptLabel: "Có",
    rejectLabel: "Hủy",
    onAccept: () => deleteTemplate(template),
    onReject: () => {
      // Do nothing on reject
    },
  });
};

// Handle save template
const handleSaveTemplate = async (template: TemplateItem) => {
  isSaving.value = true;

  try {
    // Get current templates
    const templatesSetting = getSetting("templates", "templates");
    let allTemplates: TemplateItem[] = [];

    if (templatesSetting?.value?.templates) {
      allTemplates = [...templatesSetting.value.templates];
    }

    if (isEditing.value && selectedTemplate.value) {
      // Update existing template
      const index = allTemplates.findIndex((t) => t.id === template.id);
      if (index !== -1) {
        allTemplates[index] = { ...template };
        console.log("Updated template at index:", index, template);
      }
    } else {
      // Add new template
      // Check if ID already exists
      if (allTemplates.some((t) => t.id === template.id)) {
        console.error("Template ID already exists:", template.id);
        isSaving.value = false;
        return;
      }

      allTemplates.push({ ...template });
      console.log("Added new template:", template);
    }

    // Save to server
    console.log("Saving templates to server:", allTemplates);
    const success = await syncSettingValue("templates", "templates", { templates: allTemplates });

    if (success) {
      console.log("Template saved successfully, reloading templates");
      // Force refresh the store
      await configStore.init({ category: "", name: "" });
      // Reload templates
      await loadTemplates();
      // Refresh templates in the template plugin
      await refreshTemplates();
      resetForm();
    } else {
      console.error("Failed to save template");
    }
  } catch (error) {
    console.error("Error saving template:", error);
  } finally {
    isSaving.value = false;
  }
};

// Delete template
const deleteTemplate = async (templateToDelete: TemplateItem) => {
  try {
    // Get current templates
    const templatesSetting = getSetting("templates", "templates");
    let allTemplates: TemplateItem[] = [];

    if (templatesSetting?.value?.templates) {
      allTemplates = [...templatesSetting.value.templates];
    }

    // Filter out the template to delete
    const filteredTemplates = allTemplates.filter((t) => t.id !== templateToDelete.id);

    // Save to server
    const success = await syncSettingValue("templates", "templates", {
      templates: filteredTemplates,
    });

    if (success) {
      console.log("Template deleted successfully, reloading templates");
      // Force refresh the store
      await configStore.init({ category: "", name: "" });
      // Reload templates
      await loadTemplates();
      // Refresh templates in the template plugin
      await refreshTemplates();
    } else {
      console.error("Failed to delete template");
    }
  } catch (error) {
    console.error("Error deleting template:", error);
  }
};

// Reset form
const resetForm = () => {
  selectedTemplate.value = null;
  currentTemplate.value = { ...emptyTemplate };
  isEditing.value = false; // Set to false to hide the form
};

// Create new template
const createNewTemplate = () => {
  selectedTemplate.value = null;
  currentTemplate.value = { ...emptyTemplate };
  isEditing.value = true; // Set to true to show the form for creating a new template
};

// Watch for visibility changes
watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      loadTemplates();
    }
  },
);

// Create a ref for dialog visibility
const visible = ref(props.visible);

// Update visible state
watch(
  () => visible.value,
  (newValue) => {
    emit("update:visible", newValue);
  },
);

// Update local ref when prop changes
watch(
  () => props.visible,
  (newValue) => {
    visible.value = newValue;
  },
);

// Load templates on mount
onMounted(async () => {
  await loadTemplates();
});
</script>
