/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddressInfo: typeof import('./src/components/InfoText/AddressInfo.vue')['default']
    AdministrativeInfo: typeof import('./src/components/Print/PrintOutpatientMedical/AdministrativeInfo.vue')['default']
    AppointmentForm: typeof import('./src/components/Appointment/AppointmentForm.vue')['default']
    AppointmentInfo: typeof import('./src/components/InfoText/AppointmentInfo.vue')['default']
    AppointmentSlot: typeof import('./src/components/Appointment/AppointmentSlot.vue')['default']
    AttachmentDiscountInput: typeof import('./src/components/Attachment/AttachmentDiscountInput.vue')['default']
    AttachmentItem: typeof import('./src/components/Attachment/AttachmentItem.vue')['default']
    AttachmentModal: typeof import('./src/components/Attachment/AttachmentModal.vue')['default']
    AttachmentTree: typeof import('./src/components/Attachment/AttachmentTree.vue')['default']
    AttachmentTreeLite: typeof import('./src/components/Attachment/AttachmentTreeLite.vue')['default']
    AttachmentTreePro: typeof import('./src/components/Attachment/AttachmentTreePro.vue')['default']
    Badge: typeof import('primevue/badge')['default']
    Button: typeof import('primevue/button')['default']
    ButtonGroup: typeof import('primevue/buttongroup')['default']
    Checkbox: typeof import('primevue/checkbox')['default']
    Chip: typeof import('primevue/chip')['default']
    ConfirmPopup: typeof import('primevue/confirmpopup')['default']
    ConsultationAppointmentModal: typeof import('./src/components/Appointment/ConsultationAppointmentModal.vue')['default']
    DarkModeSwitcher: typeof import('./src/components/DarkModeSwitcher/DarkModeSwitcher.vue')['default']
    DataTable: typeof import('./src/components/DataTable/DataTable.vue')['default']
    DataTableFooter: typeof import('./src/components/DataTable/DataTableFooter.vue')['default']
    DataView: typeof import('primevue/dataview')['default']
    DatePicker: typeof import('primevue/datepicker')['default']
    DateTimeInfo: typeof import('./src/components/InfoText/DateTimeInfo.vue')['default']
    DateTimeInput: typeof import('./src/components/DateTimeInput/DateTimeInput.vue')['default']
    DealFilterPreset: typeof import('./src/components/Deal/DealFilterPreset.vue')['default']
    DealName: typeof import('./src/components/Deal/DealName.vue')['default']
    DealPaymentProgress: typeof import('./src/components/Deal/DealPaymentProgress.vue')['default']
    DealSelectorPopover: typeof import('./src/components/Deal/DealSelectorPopover.vue')['default']
    DeleteConfirmation: typeof import('./src/components/WysiwgEditor/components/DeleteConfirmation.vue')['default']
    Dialog: typeof import('primevue/dialog')['default']
    Divider: typeof import('primevue/divider')['default']
    DonutChart: typeof import('./src/components/DonutChart/DonutChart.vue')['default']
    Drawer: typeof import('primevue/drawer')['default']
    DynamicSetting: typeof import('./src/components/Settings/DynamicSetting.vue')['default']
    EntityTags: typeof import('./src/components/Tags/EntityTags.vue')['default']
    ExaminationInfo: typeof import('./src/components/Print/PrintOutpatientMedical/ExaminationInfo.vue')['default']
    Fieldset: typeof import('primevue/fieldset')['default']
    FormField: typeof import('./src/components/Form/FormField.vue')['default']
    FullScreenSwitcher: typeof import('./src/components/FullScreenSwitcher/FullScreenSwitcher.vue')['default']
    GenderInfo: typeof import('./src/components/InfoText/GenderInfo.vue')['default']
    HistoryModal: typeof import('./src/components/Timeline/HistoryModal.vue')['default']
    HistoryTimeline: typeof import('./src/components/Timeline/HistoryTimeline.vue')['default']
    HistoryTimelineV2: typeof import('./src/components/Timeline/HistoryTimelineV2.vue')['default']
    HorizontalBarChart: typeof import('./src/components/HorizontalBarChart/HorizontalBarChart.vue')['default']
    IconField: typeof import('primevue/iconfield')['default']
    InputGroup: typeof import('primevue/inputgroup')['default']
    InputGroupAddon: typeof import('primevue/inputgroupaddon')['default']
    InputIcon: typeof import('primevue/inputicon')['default']
    InputMask: typeof import('primevue/inputmask')['default']
    InputNumber: typeof import('primevue/inputnumber')['default']
    InputText: typeof import('primevue/inputtext')['default']
    LeafletMap: typeof import('./src/components/LeafletMap/LeafletMap.vue')['default']
    LineChart: typeof import('./src/components/LineChart/LineChart.vue')['default']
    Listbox: typeof import('primevue/listbox')['default']
    MainColorSwitcher: typeof import('./src/components/MainColorSwitcher/MainColorSwitcher.vue')['default']
    MedicalHeader: typeof import('./src/components/Print/PrintOutpatientMedical/MedicalHeader.vue')['default']
    MedicalHistory: typeof import('./src/components/Print/PrintOutpatientMedical/MedicalHistory.vue')['default']
    MentionList: typeof import('./src/components/WysiwgEditor/MentionList.vue')['default']
    Menubar: typeof import('primevue/menubar')['default']
    Message: typeof import('primevue/message')['default']
    MessageList: typeof import('./src/components/Messages/MessageList.vue')['default']
    MessageModal: typeof import('./src/components/Messages/MessageModal.vue')['default']
    MessageSubmitConfirmation: typeof import('./src/components/Messages/MessageSubmitConfirmation.vue')['default']
    MobileMenu: typeof import('./src/components/MobileMenu/MobileMenu.vue')['default']
    MultiSelect: typeof import('primevue/multiselect')['default']
    NoteEditor: typeof import('./src/components/WysiwgEditor/NoteEditor.vue')['default']
    NoteInfo: typeof import('./src/components/InfoText/NoteInfo.vue')['default']
    OperationSelect: typeof import('./src/components/Operation/OperationSelect.vue')['default']
    OrderList: typeof import('primevue/orderlist')['default']
    Paginator: typeof import('primevue/paginator')['default']
    Password: typeof import('primevue/password')['default']
    PersonAvatar: typeof import('./src/components/Person/PersonAvatar.vue')['default']
    PersonCard: typeof import('./src/components/Person/PersonCard.vue')['default']
    PersonDeals: typeof import('./src/components/Person/PersonDeals.vue')['default']
    PersonDealsPopover: typeof import('./src/components/Deal/PersonDealsPopover.vue')['default']
    PersonFilterPreset: typeof import('./src/components/Person/PersonFilterPreset.vue')['default']
    PersonFormModal: typeof import('./src/components/Person/PersonFormModal.vue')['default']
    PersonInfoTags: typeof import('./src/components/Person/PersonInfoTags.vue')['default']
    PersonPhone: typeof import('./src/components/Person/PersonPhone.vue')['default']
    PersonSourceUrlPopup: typeof import('./src/components/Person/PersonSourceUrlPopup.vue')['default']
    PersonSubmissionPopup: typeof import('./src/components/Person/PersonSubmissionPopup.vue')['default']
    PersonTrackHistory: typeof import('./src/components/Person/PersonTrackHistory.vue')['default']
    PieChart: typeof import('./src/components/PieChart/PieChart.vue')['default']
    PipelineLayout: typeof import('./src/components/PipelineLayout.vue')['default']
    PipelineStageSelect: typeof import('./src/components/PipelineStageSelect.vue')['default']
    Popover: typeof import('primevue/popover')['default']
    PopSetting: typeof import('./src/components/Settings/PopSetting.vue')['default']
    PrintDealDetail: typeof import('./src/components/Print/PrintDealDetail.vue')['default']
    PrintLayout: typeof import('./src/components/Print/PrintLayout.vue')['default']
    PrintMedicalExam: typeof import('./src/components/Print/PrintMedicalExam.vue')['default']
    PrintMedication: typeof import('./src/components/Print/PrintMedication.vue')['default']
    PrintOutpatientMedical: typeof import('./src/components/Print/PrintOutpatientMedical/index.vue')['default']
    PrintPaymentDetail: typeof import('./src/components/Print/PrintPaymentDetail.vue')['default']
    PrintPersonInfo: typeof import('./src/components/Print/PrintPersonInfo.vue')['default']
    PrintPlan: typeof import('./src/components/Print/PrintPlan.vue')['default']
    PrintRefundDetail: typeof import('./src/components/Print/PrintRefundDetail.vue')['default']
    PrintTable: typeof import('./src/components/Print/PrintTable.vue')['default']
    PriorityChip: typeof import('./src/components/Chip/PriorityChip.vue')['default']
    PrioritySelect: typeof import('./src/components/Select/PrioritySelect.vue')['default']
    PrioritySelectBtn: typeof import('./src/components/Select/PrioritySelectBtn.vue')['default']
    ProductOperationTree: typeof import('./src/components/Product/ProductOperationTree.vue')['default']
    ProgressSpinner: typeof import('primevue/progressspinner')['default']
    ReportBarChart: typeof import('./src/components/ReportBarChart/ReportBarChart.vue')['default']
    ReportBarChart1: typeof import('./src/components/ReportBarChart1/ReportBarChart1.vue')['default']
    ReportDonutChart: typeof import('./src/components/ReportDonutChart/ReportDonutChart.vue')['default']
    ReportDonutChart1: typeof import('./src/components/ReportDonutChart1/ReportDonutChart1.vue')['default']
    ReportDonutChart2: typeof import('./src/components/ReportDonutChart2/ReportDonutChart2.vue')['default']
    ReportLineChart: typeof import('./src/components/ReportLineChart/ReportLineChart.vue')['default']
    ReportPieChart: typeof import('./src/components/ReportPieChart/ReportPieChart.vue')['default']
    RichTextNote: typeof import('./src/components/WysiwgEditor/RichTextNote.vue')['default']
    RootAttachment: typeof import('./src/components/Attachment/RootAttachment.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchAttachment: typeof import('./src/components/Attachment/SearchAttachment.vue')['default']
    SearchDiscount: typeof import('./src/components/Discount/SearchDiscount.vue')['default']
    SearchPeople: typeof import('./src/components/Person/SearchPeople.vue')['default']
    SearchPerson: typeof import('./src/components/Person/SearchPerson.vue')['default']
    SearchProduct: typeof import('./src/components/Product/SearchProduct.vue')['default']
    Select: typeof import('primevue/select')['default']
    SelectButton: typeof import('primevue/selectbutton')['default']
    SettingCheckbox: typeof import('./src/components/Settings/SettingCheckbox.vue')['default']
    SettingMultiNumberInput: typeof import('./src/components/Settings/SettingMultiNumberInput.vue')['default']
    SettingSelectButton: typeof import('./src/components/Settings/SettingSelectButton.vue')['default']
    SettingTextInput: typeof import('./src/components/Settings/SettingTextInput.vue')['default']
    SettingToggleButton: typeof import('./src/components/Settings/SettingToggleButton.vue')['default']
    SettingToggleSwitch: typeof import('./src/components/Settings/SettingToggleSwitch.vue')['default']
    SimpleLineChart: typeof import('./src/components/SimpleLineChart/SimpleLineChart.vue')['default']
    SimpleLineChart1: typeof import('./src/components/SimpleLineChart1/SimpleLineChart1.vue')['default']
    SimpleLineChart2: typeof import('./src/components/SimpleLineChart2/SimpleLineChart2.vue')['default']
    SimpleLineChart3: typeof import('./src/components/SimpleLineChart3/SimpleLineChart3.vue')['default']
    SimpleLineChart4: typeof import('./src/components/SimpleLineChart4/SimpleLineChart4.vue')['default']
    Skeleton: typeof import('primevue/skeleton')['default']
    Slider: typeof import('primevue/slider')['default']
    SplitButton: typeof import('primevue/splitbutton')['default']
    StackedBarChart: typeof import('./src/components/StackedBarChart/StackedBarChart.vue')['default']
    StackedBarChart1: typeof import('./src/components/StackedBarChart1/StackedBarChart1.vue')['default']
    StageInfo: typeof import('./src/components/InfoText/StageInfo.vue')['default']
    StateSelectBtn: typeof import('./src/components/Select/StateSelectBtn.vue')['default']
    StatisticCard: typeof import('./src/components/Card/StatisticCard.vue')['default']
    StatusChangePopover: typeof import('./src/components/Appointment/StatusChangePopover.vue')['default']
    StatusChip: typeof import('./src/components/Chip/StatusChip.vue')['default']
    SummaryAndDischarge: typeof import('./src/components/Print/PrintOutpatientMedical/SummaryAndDischarge.vue')['default']
    Tab: typeof import('primevue/tab')['default']
    TabList: typeof import('primevue/tablist')['default']
    TabPanel: typeof import('primevue/tabpanel')['default']
    TabPanels: typeof import('primevue/tabpanels')['default']
    Tabs: typeof import('primevue/tabs')['default']
    Tag: typeof import('primevue/tag')['default']
    TagCategorySelect: typeof import('./src/components/Tags/TagCategorySelect.vue')['default']
    TagChip: typeof import('./src/components/Tags/TagChip.vue')['default']
    TagChips: typeof import('./src/components/Tags/TagChips.vue')['default']
    TagSelector: typeof import('./src/components/Tags/TagSelector.vue')['default']
    TagSelectorPopover: typeof import('./src/components/Tags/TagSelectorPopover.vue')['default']
    TeethSelect: typeof import('./src/components/TeethSelect.vue')['default']
    TemplateForm: typeof import('./src/components/WysiwgEditor/components/TemplateForm.vue')['default']
    TemplateList: typeof import('./src/components/WysiwgEditor/TemplateList.vue')['default']
    TemplateManager: typeof import('./src/components/WysiwgEditor/TemplateManager.vue')['default']
    TemplatePreview: typeof import('./src/components/WysiwgEditor/components/TemplatePreview.vue')['default']
    TermInfo: typeof import('./src/components/InfoText/TermInfo.vue')['default']
    Textarea: typeof import('primevue/textarea')['default']
    Timeline: typeof import('primevue/timeline')['default']
    TimelineModal: typeof import('./src/components/Timeline/TimelineModal.vue')['default']
    TimeSlotSelector: typeof import('./src/components/Appointment/TimeSlotSelector.vue')['default']
    TinyToothChart: typeof import('./src/components/TinyToothChart.vue')['default']
    Tiptap: typeof import('./src/components/WysiwgEditor/Tiptap.vue')['default']
    TiptapTest: typeof import('./src/components/WysiwgEditor/TiptapTest.vue')['default']
    ToggleButton: typeof import('primevue/togglebutton')['default']
    ToggleSwitch: typeof import('primevue/toggleswitch')['default']
    TopBackButton: typeof import('./src/components/TopBackButton/TopBackButton.vue')['default']
    TopBar: typeof import('./src/components/TopBar/TopBar.vue')['default']
    TrackCard: typeof import('./src/components/Deal/TrackCard.vue')['default']
    TrackHistoryCard: typeof import('./src/components/Track/TrackHistoryCard.vue')['default']
    TrackInfo: typeof import('./src/components/Deal/TrackInfo.vue')['default']
    TrackUserGroup: typeof import('./src/components/Track/TrackUserGroup.vue')['default']
    TreeSelect: typeof import('primevue/treeselect')['default']
    UserAssign: typeof import('./src/components/User/UserAssign.vue')['default']
    UserAssignPromax: typeof import('./src/components/User/UserAssignPromax.vue')['default']
    UserAvatar: typeof import('./src/components/User/UserAvatar.vue')['default']
    UserAvatarGroup: typeof import('./src/components/User/UserAvatarGroup.vue')['default']
    UserMultiAssign: typeof import('./src/components/User/UserMultiAssign.vue')['default']
    VerticalBarChart: typeof import('./src/components/VerticalBarChart/VerticalBarChart.vue')['default']
  }
  export interface ComponentCustomProperties {
    Tooltip: typeof import('primevue/tooltip')['default']
  }
}
